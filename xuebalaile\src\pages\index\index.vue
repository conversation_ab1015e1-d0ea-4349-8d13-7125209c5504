<template>
  <view class="container">

    <view class="content">
      <view class="welcome">
        <text class="welcome-text">{{ getGreeting() }}</text>
        <text class="slogan">快乐学习，轻松成长</text>
      </view>

      <view class="ranking-section">
        <text class="section-title">今日排行榜</text>
        <view class="ranking-list">
          <view v-if="rankings.length === 0" class="empty-ranking">
            <text>今日暂无排行数据</text>
          </view>
          <view v-else class="ranking-item" v-for="(item, index) in rankings" :key="index">
            <view class="ranking-rank" :class="{ 'top-rank': index < 3 }">{{ item.rank }}</view>
            <image class="ranking-avatar" :src="item.avatar" mode="aspectFill" />
            <text class="ranking-name">{{ item.nickname }}</text>
            <text class="ranking-score">{{ item.score }}分</text>
          </view>
        </view>
      </view>

      <view class="feature-list">
        <view class="feature-item" @click="goToMathQuiz">
          <image class="feature-icon" src="/static/images/math.png" mode="aspectFit" />
          <text class="feature-name">口算练习</text>
          <text class="feature-desc">提高计算能力，锻炼思维反应</text>
        </view>
        <view class="feature-item" @click="goToArena">
          <image class="feature-icon" src="/static/images/arena.png" mode="aspectFit" />
          <text class="feature-name">学霸擂台</text>
          <text class="feature-desc">与其他学霸一起比拼，看谁更厉害</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onLoad, onShow } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { userInfo, isLoggedIn, initUserState } from '../../store/user';
import { getTodayRankings } from '../../api/arena';

// 排行榜数据
const rankings = ref<any[]>([]);

// 页面加载时初始化用户状态
onLoad(() => {
  initUserState();
  loadRankings();
});

// 页面显示时检查登录状态
onShow(() => {
  initUserState();
  loadRankings();
});

// 加载排行榜数据
const loadRankings = async () => {
  try {
    const res = await getTodayRankings();
    rankings.value = res;
  } catch (error) {
    console.error('获取排行榜失败:', error);
  }
};

// 跳转到登录页
const goToLogin = () => {
  uni.navigateTo({ url: '/pages/login/login' });
};

// 跳转到口算练习
const goToMathQuiz = () => {
  uni.switchTab({ url: '/pages/math/select' });
};

// 跳转到学霸擂台
const goToArena = () => {
  uni.switchTab({ url: '/pages/arena/index' });
};

// 获取问候语
const getGreeting = () => {
  const hour = new Date().getHours();
  if (hour >= 5 && hour < 12) return '上午好!';
  if (hour >= 12 && hour < 18) return '下午好!';
  return '晚上好，早睡早起!';
};

</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}


.content {
  flex: 1;
  padding: 30rpx;
}

.welcome {
  margin-bottom: 50rpx;
  text-align: center;
}

.welcome-text {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.slogan {
  font-size: 28rpx;
  color: #666666;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.feature-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.feature-name {
  font-size: 34rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.feature-desc {
  font-size: 26rpx;
  color: #999999;
  text-align: center;
}

.nickname-text {
  color: #ff0000;
  /* 红色 */
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.ranking-section {
  margin-bottom: 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.ranking-rank {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24rpx;
  font-weight: bold;
  color: #666666;
  margin-right: 15rpx;
}

.top-rank {
  background-color: #ffcc00;
  color: #ffffff;
}

.ranking-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 15rpx;
}

.ranking-name {
  flex: 1;
  font-size: 26rpx;
  color: #333333;
}

.ranking-score {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b00;
}

.empty-ranking {
  padding: 30rpx 0;
  text-align: center;
  color: #999999;
  font-size: 26rpx;
}
</style>
