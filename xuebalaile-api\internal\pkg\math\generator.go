package math

import (
	"fmt"
	"math/rand"
	"time"

	"github.com/google/uuid"
)

// MathLevel 口算难度级别
type MathLevel string

const (
	// Level10 10以内加减法
	Level10 MathLevel = "10以内"
	// Level20Simple 20以内不进位/不借位
	Level20Simple MathLevel = "20以内不进位/不借位"
	// Level20Complex 20以内进位/借位
	Level20Complex MathLevel = "20以内进位/借位"
	// Level10_10 整十数加减整十数
	Level10_10 MathLevel = "整十数加减整十数"
	// Level100Simple 100以内不进位/不借位
	Level100Simple MathLevel = "100以内不进位/不借位"
	// Level100Complex 100以内进位/借位
	Level100Complex MathLevel = "100以内进位/借位"
)

// MathType 口算类型
type MathType string

const (
	// Addition 加法
	Addition MathType = "addition"
	// Subtraction 减法
	Subtraction MathType = "subtraction"
)

// Question 口算题目
type Question struct {
	ID       string    `json:"id"`
	Question string    `json:"question"`
	Answer   int       `json:"answer"`
	Type     MathType  `json:"type"`
	Level    MathLevel `json:"level"`
}

// Generator 口算题目生成器
type Generator struct {
	rnd *rand.Rand
}

// NewGenerator 创建口算题目生成器
func NewGenerator() *Generator {
	return &Generator{
		rnd: rand.New(rand.NewSource(time.Now().UnixNano())),
	}
}

// GenerateQuestions 生成口算题目
func (g *Generator) GenerateQuestions(level MathLevel, count int) []Question {
	questions := make([]Question, 0, count)
	for i := 0; i < count; i++ {
		q := g.generateQuestion(level)
		questions = append(questions, q)
	}
	return questions
}

// generateQuestion 生成单个口算题目
func (g *Generator) generateQuestion(level MathLevel) Question {
	var a, b, answer int
	var questionStr string
	var mathType MathType

	// 随机选择加法或减法
	if g.rnd.Intn(2) == 0 {
		mathType = Addition
	} else {
		mathType = Subtraction
	}

	switch level {
	case Level10:
		// 10以内加减法
		if mathType == Addition {
			a = g.rnd.Intn(10) + 1
			b = g.rnd.Intn(10) + 1
			answer = a + b
			questionStr = fmt.Sprintf("%d + %d = ?", a, b)
		} else {
			a = g.rnd.Intn(10) + 1
			b = g.rnd.Intn(a) + 1
			answer = a - b
			questionStr = fmt.Sprintf("%d - %d = ?", a, b)
		}

	case Level20Simple:
		// 20以内不进位/不借位
		if mathType == Addition {
			a = g.rnd.Intn(19) + 1
			// 确保个位数相加不超过10
			maxB := 9 - (a % 10)
			if maxB <= 0 {
				maxB = 1
			}
			b = g.rnd.Intn(maxB) + 1
			answer = a + b
			questionStr = fmt.Sprintf("%d + %d = ?", a, b)
		} else {
			a = g.rnd.Intn(19) + 1
			// 确保个位数相减不需要借位
			maxB := a % 10
			if maxB == 0 {
				maxB = 1
			}
			b = g.rnd.Intn(maxB) + 1
			answer = a - b
			questionStr = fmt.Sprintf("%d - %d = ?", a, b)
		}

	case Level20Complex:
		// 20以内进位/借位
		if mathType == Addition {
			a = g.rnd.Intn(14) + 1
			// 确保个位数相加超过10
			minB := 11 - (a % 10)
			maxB := 20 - a
			if minB > maxB {
				minB = 1
				maxB = 9
			}
			b = g.rnd.Intn(maxB-minB+1) + minB
			answer = a + b
			questionStr = fmt.Sprintf("%d + %d = ?", a, b)
		} else {
			a = g.rnd.Intn(10) + 11 // 11-20
			// 确保个位数相减需要借位
			minB := 1
			maxB := a % 10
			if maxB == 0 {
				maxB = 9
			}
			b = g.rnd.Intn(maxB-minB+1) + minB
			answer = a - b
			questionStr = fmt.Sprintf("%d - %d = ?", a, b)
		}

	case Level10_10:
		// 整十数加减整十数
		if mathType == Addition {
			a = (g.rnd.Intn(9) + 1) * 10
			b = (g.rnd.Intn(9) + 1) * 10
			answer = a + b
			questionStr = fmt.Sprintf("%d + %d = ?", a, b)
		} else {
			a = (g.rnd.Intn(9) + 1) * 10
			b = (g.rnd.Intn(a/10) + 1) * 10
			answer = a - b
			questionStr = fmt.Sprintf("%d - %d = ?", a, b)
		}

	case Level100Simple:
		// 100以内不进位/不借位
		if mathType == Addition {
			a = g.rnd.Intn(90) + 10
			// 确保个位数相加不超过10，十位数相加不超过10
			maxBOnes := 9 - (a % 10)
			maxBTens := 9 - ((a / 10) % 10)
			if maxBOnes <= 0 {
				maxBOnes = 1
			}
			if maxBTens <= 0 {
				maxBTens = 1
			}
			bOnes := g.rnd.Intn(maxBOnes) + 1
			bTens := g.rnd.Intn(maxBTens) + 1
			b = bTens*10 + bOnes
			answer = a + b
			questionStr = fmt.Sprintf("%d + %d = ?", a, b)
		} else {
			a = g.rnd.Intn(90) + 10
			// 确保个位数相减不需要借位，十位数相减不需要借位
			maxBOnes := a % 10
			maxBTens := (a / 10) % 10
			if maxBOnes == 0 {
				maxBOnes = 1
			}
			if maxBTens == 0 {
				maxBTens = 1
			}
			bOnes := g.rnd.Intn(maxBOnes) + 1
			bTens := g.rnd.Intn(maxBTens) + 1
			b = bTens*10 + bOnes
			answer = a - b
			questionStr = fmt.Sprintf("%d - %d = ?", a, b)
		}

	case Level100Complex:
		// 100以内进位/借位
		if mathType == Addition {
			a = g.rnd.Intn(90) + 10
			// 确保个位数相加超过10或十位数相加超过10
			if g.rnd.Intn(2) == 0 {
				// 个位数进位
				minBOnes := 11 - (a % 10)
				if minBOnes < 1 {
					minBOnes = 1
				}
				if minBOnes > 10 {
					minBOnes = 10
				}
				rangeSize := 10 - minBOnes + 1
				if rangeSize <= 0 {
					rangeSize = 1
				}
				bOnes := g.rnd.Intn(rangeSize) + minBOnes
				bTens := g.rnd.Intn(9) + 1
				b = bTens*10 + bOnes
			} else {
				// 十位数进位
				bOnes := g.rnd.Intn(10)
				minBTens := 11 - ((a / 10) % 10)
				maxBTens := 9
				if minBTens > maxBTens {
					minBTens = 1
				}
				bTens := g.rnd.Intn(maxBTens-minBTens+1) + minBTens
				b = bTens*10 + bOnes
			}
			answer = a + b
			questionStr = fmt.Sprintf("%d + %d = ?", a, b)
		} else {
			a = g.rnd.Intn(90) + 10
			// 确保个位数相减需要借位或十位数相减需要借位
			if g.rnd.Intn(2) == 0 {
				// 个位数借位
				bOnes := g.rnd.Intn(9) + 1
				bTens := g.rnd.Intn((a/10)%10) + 1
				if a%10 < bOnes {
					b = bTens*10 + bOnes
				} else {
					// 确保需要借位
					b = bTens*10 + (a%10 + g.rnd.Intn(9) + 1)
					if b >= a {
						b = a - 1
					}
				}
			} else {
				// 十位数借位
				bOnes := g.rnd.Intn(10)
				bTens := ((a / 10) % 10) + g.rnd.Intn(5) + 1
				if bTens >= 10 {
					bTens = 9
				}
				b = bTens*10 + bOnes
				if b >= a {
					b = a - 1
				}
			}
			answer = a - b
			questionStr = fmt.Sprintf("%d - %d = ?", a, b)
		}
	}

	return Question{
		ID:       uuid.New().String(),
		Question: questionStr,
		Answer:   answer,
		Type:     mathType,
		Level:    level,
	}
}
