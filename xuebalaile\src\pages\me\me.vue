<template>
  <view class="container">
    <view class="header">
        <image class="avatar" :src="userInfo.avatar" mode="aspectFill" />
        <text class="nickname">欢迎 <text class="nickname-text">{{ userInfo.nickname || '小学霸' }}</text></text>
    </view>

    <view class="content">
    </view>
  </view>
</template>

<script setup lang="ts">
import { onLoad, onShow } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { userInfo } from '../../store/user';

onLoad(() => {
});

onShow(() => {
});

</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 40rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  background-color: #3cc51f;
  color: #ffffff;
  border-radius: 8rpx;
  font-size: 30rpx;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.nickname-text {
  color: #ff0000;
  /* 红色 */
}


</style>
