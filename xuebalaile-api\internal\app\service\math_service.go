package service

import (
	"errors"

	"xuebalaile-api/internal/app/model"
	"xuebalaile-api/internal/app/repository"
	"xuebalaile-api/internal/pkg/math"
)

// MathService 口算服务接口
type MathService interface {
	GenerateQuestions(level math.MathLevel) ([]math.Question, error)
	SubmitResult(userID uint, level math.MathLevel, answers []AnswerSubmission, timeUsed int) (*model.MathResultResponse, error)
	GetWrongQuestions(userID uint) ([]model.WrongQuestionResponse, error)
	SubmitWrongQuestionAnswer(userID uint, questionID string, answer int) (bool, int, error)
	GetCompetitionLevels() ([]model.CompetitionLevelResponse, error)
}

// AnswerSubmission 答案提交
type AnswerSubmission struct {
	QuestionID string `json:"questionId"`
	UserAnswer int    `json:"userAnswer"`
	IsCorrect  bool   `json:"isCorrect"`
}

// mathService 口算服务实现
type mathService struct {
	mathRepo      repository.MathRepository
	mathGenerator *math.Generator
}

// NewMathService 创建口算服务
func NewMathService(mathRepo repository.MathRepository) MathService {
	return &mathService{
		mathRepo:      mathRepo,
		mathGenerator: math.NewGenerator(),
	}
}

// GenerateQuestions 生成口算题目
func (s *mathService) GenerateQuestions(level math.MathLevel) ([]math.Question, error) {
	// 生成200道题目
	questions := s.mathGenerator.GenerateQuestions(level, 200)

	// 保存题目到数据库
	if err := s.mathRepo.SaveQuestions(questions); err != nil {
		return nil, err
	}

	return questions, nil
}

// SubmitResult 提交答题结果
func (s *mathService) SubmitResult(userID uint, level math.MathLevel, answers []AnswerSubmission, timeUsed int) (*model.MathResultResponse, error) {
	// 统计正确和错误的题目数量
	totalQuestions := len(answers)
	correctCount := 0
	wrongQuestions := make([]*model.WrongQuestion, 0)

	for _, answer := range answers {
		// 获取题目信息
		question, err := s.mathRepo.FindQuestionByID(answer.QuestionID)
		if err != nil {
			return nil, err
		}
		if question == nil {
			return nil, errors.New("question not found")
		}

		// 检查答案是否正确
		isCorrect := question.Answer == answer.UserAnswer
		if isCorrect {
			correctCount++
		} else {
			// 添加到错题集
			wrongQuestion := &model.WrongQuestion{
				UserID:      userID,
				QuestionID:  question.QuestionID,
				Question:    question.Question,
				Answer:      question.Answer,
				UserAnswer:  answer.UserAnswer,
				Type:        question.Type,
				Level:       question.Level,
				IsCorrected: false,
			}
			wrongQuestions = append(wrongQuestions, wrongQuestion)
		}
	}

	// 计算得分
	wrongCount := totalQuestions - correctCount
	score := float64(correctCount) / float64(totalQuestions) * 100

	// 保存结果
	result := &model.MathResult{
		UserID:         userID,
		Level:          string(level),
		TotalQuestions: totalQuestions,
		CorrectCount:   correctCount,
		WrongCount:     wrongCount,
		TimeUsed:       timeUsed,
		Score:          score,
	}

	if err := s.mathRepo.SaveResult(result); err != nil {
		return nil, err
	}

	// 保存错题
	if len(wrongQuestions) > 0 {
		if err := s.mathRepo.SaveWrongQuestions(wrongQuestions); err != nil {
			return nil, err
		}
	}

	// 转换错题为响应格式
	wrongQuestionResponses := make([]model.WrongQuestionResponse, len(wrongQuestions))
	for i, wq := range wrongQuestions {
		wrongQuestionResponses[i] = *wq.ToResponse()
	}

	return result.ToResponse(wrongQuestionResponses), nil
}

// GetWrongQuestions 获取错题集
func (s *mathService) GetWrongQuestions(userID uint) ([]model.WrongQuestionResponse, error) {
	// 获取用户的错题
	wrongQuestions, err := s.mathRepo.GetWrongQuestionsByUserID(userID)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	responses := make([]model.WrongQuestionResponse, len(wrongQuestions))
	for i, wq := range wrongQuestions {
		responses[i] = *wq.ToResponse()
	}

	return responses, nil
}

// SubmitWrongQuestionAnswer 提交错题答案
func (s *mathService) SubmitWrongQuestionAnswer(userID uint, questionID string, answer int) (bool, int, error) {
	// 获取题目信息
	question, err := s.mathRepo.FindQuestionByID(questionID)
	if err != nil {
		return false, 0, err
	}
	if question == nil {
		return false, 0, errors.New("question not found")
	}

	// 检查答案是否正确
	isCorrect := question.Answer == answer

	// 如果正确，更新错题状态
	if isCorrect {
		// 获取用户的错题
		wrongQuestions, err := s.mathRepo.GetWrongQuestionsByUserID(userID)
		if err != nil {
			return false, 0, err
		}

		// 查找对应的错题
		for _, wq := range wrongQuestions {
			if wq.QuestionID == questionID {
				wq.IsCorrected = true
				if err := s.mathRepo.UpdateWrongQuestion(wq); err != nil {
					return false, 0, err
				}
				break
			}
		}
	}

	return isCorrect, question.Answer, nil
}

// GetCompetitionLevels 获取练习级别列表
func (s *mathService) GetCompetitionLevels() ([]model.CompetitionLevelResponse, error) {
	// 返回预定义的练习级别列表
	levels := []model.CompetitionLevelResponse{
		{
			Name:        "10以内加减法",
			Value:       math.Level10,
			Description: "适合幼儿园大班至小学一年级",
		},
		{
			Name:        "20以内不进位/不借位",
			Value:       math.Level20Simple,
			Description: "适合小学一年级",
		},
		{
			Name:        "20以内进位/借位",
			Value:       math.Level20Complex,
			Description: "适合小学一年级至二年级",
		},
		{
			Name:        "整十数加减整十数",
			Value:       math.Level10_10,
			Description: "适合小学一年级至二年级",
		},
		{
			Name:        "100以内不进位/不借位",
			Value:       math.Level100Simple,
			Description: "适合小学二年级",
		},
		{
			Name:        "100以内进位/借位",
			Value:       math.Level100Complex,
			Description: "适合小学二年级至三年级",
		},
	}

	return levels, nil
}
