package handler

import (
	"net/http"

	"xuebalaile-api/internal/app/service"

	"github.com/gin-gonic/gin"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService service.UserService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService service.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// Login 用户登录
func (h *UserHandler) Login(c *gin.Context) {
	var req struct {
		Code     string `json:"code" binding:"required"`
		Platform string `json:"platform" binding:"required,oneof=wechat xiaohongshu"`
	}

	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	user, token, expiresAt, notBefore, err := h.userService.Login(req.Code, req.Platform)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data": gin.H{
			"token":     token,
			"expiresAt": expiresAt,
			"notBefore": notBefore,
			"userInfo":  user,
		},
	})
}

// GetUserInfo 获取用户信息
func (h *UserHandler) GetUserInfo(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Unauthorized",
		})
		return
	}

	user, err := h.userService.GetUserByID(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    user,
	})
}

// RegisterRoutes 注册路由
func (h *UserHandler) RegisterRoutes(router *gin.RouterGroup, authMiddleware gin.HandlerFunc) {
	router.POST("/login", h.Login)

	authRouter := router.Group("/")
	authRouter.Use(authMiddleware)
	{
		authRouter.GET("/info", h.GetUserInfo)
	}
}
