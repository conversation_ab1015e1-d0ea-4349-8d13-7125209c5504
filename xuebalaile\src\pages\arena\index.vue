<template>
  <view class="container">
    <view class="header">
      <text class="title">学霸擂台</text>
      <text class="subtitle">与其他学霸一起比拼，看谁更厉害！</text>
    </view>

    <view class="content">
      <view class="section">
        <text class="section-title">选择难度</text>
        <view class="level-list">
          <view
            v-for="(level, index) in levels"
            :key="index"
            class="level-item"
            @click="selectLevel(level.value)"
          >
            <view class="level-card">
              <text class="level-name">{{ level.name }}</text>
              <text class="level-desc">{{ level.description }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="section">
        <text class="section-title">今日排行榜</text>
        <view class="ranking-list">
          <view v-if="rankings.length === 0" class="empty-ranking">
            <text>今日暂无排行数据</text>
          </view>
          <view v-else class="ranking-item" v-for="(item, index) in rankings" :key="index">
            <view class="ranking-rank" :class="{ 'top-rank': index < 3 }">{{ item.rank }}</view>
            <image class="ranking-avatar" :src="item.avatar" mode="aspectFill" />
            <text class="ranking-name">{{ item.nickname }}</text>
            <text class="ranking-score">{{ item.score }}分</text>
          </view>
        </view>
      </view>

      <view class="section">
        <text class="section-title">我的战绩</text>
        <view class="history-list">
          <view v-if="userRankings.length === 0" class="empty-history">
            <text>暂无战绩记录</text>
          </view>
          <view v-else class="history-item" v-for="(item, index) in userRankings" :key="index">
            <view class="history-date">{{ item.rankDate }}</view>
            <view class="history-info">
              <text class="history-score">{{ item.score }}分</text>
              <text class="history-rank">第{{ item.rank }}名</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { getArenaLevels, getTodayRankings, getUserRankings, arenaSocket } from '../../api/arena';
import { joinRoom, initArenaEvents, cleanupArena } from '../../store/arena';
import { isLoggedIn } from '../../store/user';

// 难度级别列表
const levels = ref<any[]>([]);

// 排行榜数据
const rankings = ref<any[]>([]);

// 用户战绩
const userRankings = ref<any[]>([]);

// 页面加载时
onMounted(async () => {
  // 初始化WebSocket事件监听
  initArenaEvents();
  
  // 连接WebSocket
  arenaSocket.connect();
  
  // 获取难度级别列表
  try {
    const res = await getArenaLevels();
    levels.value = res;
  } catch (error) {
    console.error('获取难度级别失败:', error);
  }
  
  // 获取今日排行榜
  try {
    const res = await getTodayRankings();
    rankings.value = res;
  } catch (error) {
    console.error('获取排行榜失败:', error);
  }
  
  // 获取用户战绩
  if (isLoggedIn.value) {
    try {
      const res = await getUserRankings();
      userRankings.value = res;
    } catch (error) {
      console.error('获取用户战绩失败:', error);
    }
  }
  
  // 监听房间状态更新
  arenaSocket.on('room_status', (data) => {
    // 跳转到等待房间页面
    uni.navigateTo({ url: '/pages/arena/room' });
  });
});

// 页面卸载时
onUnmounted(() => {
  // 清理资源
  cleanupArena();
  
  // 移除事件监听
  arenaSocket.off('room_status');
});

// 选择难度级别
const selectLevel = (level: string) => {
  if (!isLoggedIn.value) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再参与学霸擂台',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({ url: '/pages/login/login' });
        }
      }
    });
    return;
  }
  
  // 显示加载中
  uni.showLoading({ title: '正在加入...' });
  
  // 加入房间
  joinRoom(level);
  
  // 隐藏加载中
  setTimeout(() => {
    uni.hideLoading();
  }, 1000);
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 30rpx;
}

.header {
  padding: 40rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
  margin-bottom: 20rpx;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: #999999;
}

.content {
  flex: 1;
  padding: 0 30rpx;
}

.section {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.level-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.level-item {
  width: 100%;
}

.level-card {
  background-color: #f5f7fa;
  border-radius: 8rpx;
  padding: 20rpx;
  border-left: 8rpx solid #3cc51f;
}

.level-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.level-desc {
  font-size: 24rpx;
  color: #666666;
}

.ranking-list, .history-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.ranking-rank {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #666666;
  margin-right: 20rpx;
}

.top-rank {
  background-color: #ffcc00;
  color: #ffffff;
}

.ranking-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.ranking-name {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
}

.ranking-score {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff6b00;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.history-date {
  font-size: 26rpx;
  color: #999999;
}

.history-info {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.history-score {
  font-size: 30rpx;
  font-weight: bold;
  color: #ff6b00;
}

.history-rank {
  font-size: 26rpx;
  color: #666666;
}

.empty-ranking, .empty-history {
  padding: 40rpx 0;
  text-align: center;
  color: #999999;
  font-size: 28rpx;
}
</style>
