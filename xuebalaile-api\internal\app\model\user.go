package model

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	gorm.Model
	Nickname string `gorm:"size:50;not null"`
	Avatar   string `gorm:"size:255"`
	OpenID   string `gorm:"size:50;uniqueIndex"`
	Platform string `gorm:"size:20;not null"` // wechat, xiaohongshu
}

// UserResponse 用户响应
type UserResponse struct {
	ID        uint      `json:"id"`
	Nickname  string    `json:"nickname"`
	Avatar    string    `json:"avatar"`
	OpenID    string    `json:"openid"`
	Platform  string    `json:"platform"`
	CreatedAt time.Time `json:"created_at"`
}

// ToResponse 转换为响应
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:        u.ID,
		Nickname:  u.Nickname,
		Avatar:    u.Avatar,
		OpenID:    u.OpenID,
		Platform:  u.Platform,
		CreatedAt: u.CreatedAt,
	}
}
