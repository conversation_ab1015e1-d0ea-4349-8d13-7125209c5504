<template>
  <view class="container">
    <view class="header">
      <text class="title">等待开始</text>
      <text class="subtitle">{{ roomState.level }} - {{ roomState.players.length }}人已加入</text>
    </view>

    <view v-if="roomState.countdown > 0" class="countdown">
      <text class="countdown-text">{{ roomState.countdown }}秒后开始</text>
      <view class="countdown-progress" :style="{ width: (roomState.countdown / 10) * 100 + '%' }"></view>
    </view>

    <view class="content">
      <view class="section">
        <text class="section-title">参与者 ({{ roomState.players.length }})</text>
        <view class="player-list">
          <view class="player-item" v-for="(player, index) in roomState.players" :key="player.userId">
            <image class="player-avatar" :src="player.avatar" mode="aspectFill" />
            <view class="player-info">
              <text class="player-name">{{ player.nickname }}</text>
              <text class="player-status" :class="{ 'online': player.isOnline, 'offline': !player.isOnline }">
                {{ player.isOnline ? '在线' : '离线' }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <view class="section">
        <text class="section-title">比赛规则</text>
        <view class="rules">
          <view class="rule-item">
            <text class="rule-number">1</text>
            <text class="rule-text">每道题有4个选项，选择正确答案</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">2</text>
            <text class="rule-text">每题10分，答对得分，答错不扣分</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">3</text>
            <text class="rule-text">总共10道题，限时60秒完成</text>
          </view>
          <view class="rule-item">
            <text class="rule-number">4</text>
            <text class="rule-text">得分相同时，用时少的排名靠前</text>
          </view>
        </view>
      </view>
    </view>

    <view class="footer">
      <button class="leave-btn" @click="leaveRoom">退出房间</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, watch } from 'vue';
import { roomState, cleanupArena } from '../../store/arena';
import { arenaSocket } from '../../api/arena';

// 页面加载时
onMounted(() => {
  // 监听游戏开始事件
  arenaSocket.on('game_start', () => {
    // 跳转到比赛页面
    uni.navigateTo({ url: '/pages/arena/battle' });
  });
  
  // 如果房间状态为running，直接跳转到比赛页面
  if (roomState.status === 'running') {
    uni.navigateTo({ url: '/pages/arena/battle' });
  }
  
  // 如果房间状态为finished，直接跳转到结果页面
  if (roomState.status === 'finished') {
    uni.navigateTo({ url: '/pages/arena/result' });
  }
});

// 页面卸载时
onUnmounted(() => {
  // 移除事件监听
  arenaSocket.off('game_start');
});

// 监听房间状态变化
watch(() => roomState.status, (newStatus) => {
  if (newStatus === 'running') {
    // 跳转到比赛页面
    uni.navigateTo({ url: '/pages/arena/battle' });
  } else if (newStatus === 'finished') {
    // 跳转到结果页面
    uni.navigateTo({ url: '/pages/arena/result' });
  }
});

// 退出房间
const leaveRoom = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出房间吗？',
    success: (res) => {
      if (res.confirm) {
        // 发送离开房间消息
        arenaSocket.send('leave_room', {});
        
        // 返回打擂首页
        uni.navigateBack();
      }
    }
  });
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 40rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: #999999;
}

.countdown {
  position: relative;
  height: 80rpx;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.countdown-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  z-index: 1;
}

.countdown-progress {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background-color: #ffcc00;
  transition: width 1s linear;
  z-index: 0;
}

.content {
  flex: 1;
  padding: 30rpx;
}

.section {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.player-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.player-item {
  width: calc(50% - 10rpx);
  display: flex;
  align-items: center;
  padding: 15rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.player-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 15rpx;
}

.player-info {
  flex: 1;
}

.player-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 5rpx;
  display: block;
}

.player-status {
  font-size: 24rpx;
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
  display: inline-block;
}

.online {
  background-color: #e8f5e9;
  color: #4caf50;
}

.offline {
  background-color: #ffebee;
  color: #f44336;
}

.rules {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.rule-item {
  display: flex;
  align-items: center;
}

.rule-number {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background-color: #3cc51f;
  color: #ffffff;
  font-size: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15rpx;
}

.rule-text {
  font-size: 28rpx;
  color: #666666;
}

.footer {
  padding: 30rpx;
}

.leave-btn {
  width: 100%;
  height: 90rpx;
  line-height: 90rpx;
  background-color: #f5f5f5;
  color: #666666;
  font-size: 30rpx;
  border-radius: 45rpx;
}
</style>
