<template>
  <view class="container">
    <view class="header">
      <view class="timer">
        <text class="timer-label">倒计时：</text>
        <text class="timer-value" :class="{ 'timer-warning': countdownTime <= 30 }">{{ formatTime(countdownTime)
          }}</text>
      </view>
      <view class="progress">
        <text class="progress-text">{{ currentQuestionIndex + 1 }}</text>
      </view>
    </view>

    <view class="action-area">
      <button class="submit-btn" @click="submitAnswer" :disabled="!answer">提交答案</button>
    </view>
    
    <view class="question-area">
      <view class="question-card">
        <text class="question-text">{{ currentQuestion?.question }}</text>
        <view class="answer-input">
          <input type="number" v-model="answer" placeholder="请输入答案" :focus="inputFocus" @blur="handleInputBlur"
            @confirm="submitAnswer" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import {
  questions,
  currentQuestionIndex,
  userAnswers,
  countdownTime,
  submitAnswer as storeSubmitAnswer,
  submitQuiz,
  isQuizzing
} from '../../store/math';

// 当前答案
const answer = ref('');

// 输入框焦点
const inputFocus = ref(true);

// 计时器
let timer: number | null = null;

// 开始时间
const startTime = ref(Date.now());

// 当前题目
const currentQuestion = computed(() => {
  return questions[currentQuestionIndex.value] || null;
});

// 格式化时间
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};
 
// 提交答案
const submitAnswer = () => {
  if (!answer.value) return;

  // 提交答案到store
  const success = storeSubmitAnswer(Number(answer.value));

  // 清空输入
  answer.value = '';

  // 重新聚焦输入框
  inputFocus.value = false;
  setTimeout(() => {
    inputFocus.value = true;
  }, 100);

  // 如果已经是最后一题，自动提交
  if (!success) {
    finishQuiz();
  }
};

// 处理输入框失焦
const handleInputBlur = () => {
  // 延迟重新聚焦
  setTimeout(() => {
    inputFocus.value = true;
  }, 100);
};

// 完成答题
const finishQuiz = async () => {
  if (timer) {
    clearInterval(timer);
    timer = null;
  }

  // 计算用时（秒）
  const timeUsed = Math.floor((Date.now() - startTime.value) / 1000);

  // 提交答题结果
  const success = await submitQuiz(timeUsed);

  if (success) {
    // 跳转到结果页面
    uni.redirectTo({ url: '/pages/math/result' });
  } else {
    uni.showToast({
      title: '提交答题结果失败',
      icon: 'none'
    });

    // 返回选择页面
    // setTimeout(() => {
    //   uni.navigateBack();
    // }, 1500);
  }
};

// 倒计时
const startCountdown = () => {
  timer = setInterval(() => {
    if (countdownTime.value <= 0) {
      // 时间到，自动提交
      clearInterval(timer!);
      timer = null;
      finishQuiz();
      return;
    }

    countdownTime.value--;
  }, 1000) as unknown as number;
};

// 页面加载时
onMounted(() => {
  // 检查是否在答题状态
  if (!isQuizzing.value || questions.length === 0) {
    uni.showToast({
      title: '请先选择难度级别',
      icon: 'none'
    });

    // 返回选择页面
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
    return;
  }

  // 记录开始时间
  startTime.value = Date.now();

  // 开始倒计时
  startCountdown();

  // 聚焦输入框
  inputFocus.value = true;
});

// 页面卸载时
onUnmounted(() => {
  // 清除计时器
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.timer {
  display: flex;
  align-items: center;
}

.timer-label {
  font-size: 28rpx;
  color: #666666;
}

.timer-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.timer-warning {
  color: #ff3b30;
}

.progress {
  background-color: #f0f0f0;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666666;
}

.question-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx;
}

.question-card {
  width: 100%;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
}

.question-text {
  font-size: 60rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 60rpx;
}

.answer-input {
  width: 80%;
  border-bottom: 2rpx solid #dddddd;
  padding: 20rpx 0;
  margin-bottom: 40rpx;
}

.answer-input input {
  width: 100%;
  height: 80rpx;
  font-size: 48rpx;
  text-align: center;
}

.action-area {
  padding: 30rpx;
}

.submit-btn {
  height: 90rpx;
  background-color: #3cc51f;
  color: #ffffff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.submit-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}
</style>
