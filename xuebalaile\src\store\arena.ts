/**
 * 打擂状态管理
 */
import { ref, reactive } from 'vue';
import { arenaSocket } from '../api/arena';
import type { ArenaPlayer, ArenaQuestion, ArenaRoom } from '../api/arena';

// 房间状态
export const roomState = reactive<{
  roomId: string;
  status: string;
  level: string;
  players: ArenaPlayer[];
  countdown: number;
  isReconnecting: boolean;
}>({
  roomId: '',
  status: '', // waiting, running, finished
  level: '',
  players: [],
  countdown: 0,
  isReconnecting: false
});

// 比赛状态
export const battleState = reactive<{
  currentIndex: number;
  questions: ArenaQuestion[];
  currentQuestion: ArenaQuestion | null;
  timeRemaining: number;
  playerProgress: Record<string, any>;
  myAnswers: any[];
  isFinished: boolean;
}>({
  currentIndex: 0,
  questions: [],
  currentQuestion: null,
  timeRemaining: 0,
  playerProgress: {},
  myAnswers: [],
  isFinished: false
});

// 结果状态
export const resultState = reactive<{
  ranking: ArenaPlayer[];
  myResult: ArenaPlayer | null;
  totalPlayers: number;
}>({
  ranking: [],
  myResult: null,
  totalPlayers: 0
});

// 初始化WebSocket事件监听
export function initArenaEvents() {
  // 房间状态更新
  arenaSocket.on('room_status', (data: ArenaRoom) => {
    Object.assign(roomState, {
      roomId: data.roomId,
      status: data.status,
      level: data.level,
      players: data.players || [],
      countdown: data.countdown || 0
    });
  });
  
  // 倒计时更新
  arenaSocket.on('countdown', (data: { countdown: number }) => {
    roomState.countdown = data.countdown;
  });
  
  // 比赛开始
  arenaSocket.on('game_start', (data: { questions: ArenaQuestion[], timeLimit: number }) => {
    battleState.questions = data.questions || [];
    battleState.timeRemaining = data.timeLimit || 60;
    battleState.currentIndex = 0;
    battleState.currentQuestion = battleState.questions[0] || null;
    battleState.myAnswers = [];
    battleState.isFinished = false;
    
    // 开始倒计时
    startBattleTimer();
  });
  
  // 玩家进度更新
  arenaSocket.on('player_progress', (data: { progress: Record<string, any> }) => {
    battleState.playerProgress = data.progress || {};
  });
  
  // 比赛结果
  arenaSocket.on('game_result', (data: { ranking: ArenaPlayer[], myResult: ArenaPlayer, totalPlayers: number }) => {
    resultState.ranking = data.ranking || [];
    resultState.myResult = data.myResult || null;
    resultState.totalPlayers = data.totalPlayers || 0;
    battleState.isFinished = true;
  });
  
  // 重连成功
  arenaSocket.on('reconnect_success', (data: any) => {
    roomState.isReconnecting = false;
    
    // 根据房间状态恢复UI
    if (data.roomStatus === 'waiting') {
      // 恢复等待房间状态
      Object.assign(roomState, {
        roomId: data.roomId,
        status: data.roomStatus,
        level: data.level,
        players: data.players || [],
        countdown: data.countdown || 0
      });
    } else if (data.roomStatus === 'running') {
      // 恢复比赛状态
      battleState.questions = data.questions || [];
      battleState.currentIndex = data.currentIndex || 0;
      battleState.currentQuestion = battleState.questions[battleState.currentIndex] || null;
      battleState.timeRemaining = data.timeRemaining || 0;
      battleState.playerProgress = data.progress || {};
      battleState.myAnswers = data.myAnswers || [];
      battleState.isFinished = false;
      
      // 开始倒计时
      startBattleTimer();
    } else if (data.roomStatus === 'finished') {
      // 恢复结果状态
      resultState.ranking = data.ranking || [];
      resultState.myResult = data.myResult || null;
      resultState.totalPlayers = data.totalPlayers || 0;
      battleState.isFinished = true;
    }
  });
  
  // 重连失败
  arenaSocket.on('reconnect_failed', () => {
    roomState.isReconnecting = false;
    // 返回打擂首页
    uni.showToast({
      title: '重连失败，请重新开始',
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateTo({ url: '/pages/arena/index' });
    }, 1500);
  });
}

// 开始比赛倒计时
let battleTimer: number | null = null;
function startBattleTimer() {
  if (battleTimer) {
    clearInterval(battleTimer);
  }
  
  battleTimer = setInterval(() => {
    if (battleState.timeRemaining <= 0) {
      clearInterval(battleTimer!);
      // 时间到，自动提交
      arenaSocket.send('submit_all_answers', {
        answers: battleState.myAnswers
      });
      return;
    }
    
    battleState.timeRemaining--;
  }, 1000) as unknown as number;
}

// 提交答案
export function submitAnswer(optionIndex: number) {
  const currentQuestion = battleState.currentQuestion;
  if (!currentQuestion) return;
  
  // 记录答案
  battleState.myAnswers[battleState.currentIndex] = {
    questionId: currentQuestion.id,
    answer: optionIndex,
    answerTime: Date.now()
  };
  
  // 发送答案
  arenaSocket.send('submit_answer', {
    questionId: currentQuestion.id,
    answer: optionIndex,
    answerTime: Date.now()
  });
  
  // 移动到下一题
  battleState.currentIndex++;
  if (battleState.currentIndex < battleState.questions.length) {
    battleState.currentQuestion = battleState.questions[battleState.currentIndex];
  } else {
    // 所有题目已答完
    clearInterval(battleTimer!);
    arenaSocket.send('finish_battle', {
      answers: battleState.myAnswers
    });
  }
}

// 加入房间
export function joinRoom(level: string) {
  roomState.isReconnecting = false;
  arenaSocket.send('join_room', { level });
}

// 尝试重连
export function tryReconnect() {
  roomState.isReconnecting = true;
  arenaSocket.tryReconnect();
}

// 清理资源
export function cleanupArena() {
  if (battleTimer) {
    clearInterval(battleTimer);
    battleTimer = null;
  }
}

// 格式化时间
export function formatTime(seconds: number) {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
}
