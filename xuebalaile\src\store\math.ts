/**
 * 口算状态管理
 */
import { ref, reactive } from 'vue';
import { getMathQuestions, submitMathResult } from '../api/math';
import type { MathLevel, MathQuestion, MathResult } from '../api/math';

// 当前选择的难度级别
export const currentLevel = ref<MathLevel | null>(null);

// 题目列表
export const questions = reactive<MathQuestion[]>([]);

// 当前题目索引
export const currentQuestionIndex = ref(0);

// 回答的题目数量
export const answeredQuestionCount = ref(0);

// 用户答案
export const userAnswers = reactive<Record<string, number>>({});

// 答题结果
export const quizResult = reactive<MathResult>({
  totalQuestions: 0,
  correctCount: 0,
  wrongCount: 0,
  wrongQuestions: [],
  timeUsed: 0,
});

// 倒计时时间（秒）
export const countdownTime = ref(60); // 60秒
export const totalTime = ref(60); // 总时长:秒

// 是否正在答题
export const isQuizzing = ref(false);

// 设置难度级别
export const setLevel = (level: MathLevel) => {
  currentLevel.value = level;
};

// 获取题目
export const fetchQuestions = async () => {
  if (!currentLevel.value) return false;

  try {
    const res = await getMathQuestions(currentLevel.value);
    console.log("getMathQuestions res: ", res)
    questions.length = 0;
    questions.push(...res);

    // 重置状态
    currentQuestionIndex.value = 0;
    answeredQuestionCount.value = 0;
    Object.keys(userAnswers).forEach(key => delete userAnswers[key]);
    countdownTime.value = 60;
    isQuizzing.value = true;

    return true;
  } catch (error) {
    console.error('获取题目失败:', error);
    return false;
  }
};

// 提交当前题目答案
export const submitAnswer = (answer: number) => {
  const currentQuestion = questions[currentQuestionIndex.value];
  if (!currentQuestion) return false;

  userAnswers[currentQuestion.id] = answer;

  // 移动到下一题
  if (currentQuestionIndex.value < questions.length - 1) {
    currentQuestionIndex.value++;
    answeredQuestionCount.value++;
    return true;
  }
  
  return false;
};

// 提交答题结果
export const submitQuiz = async (timeUsed: number) => {
  if (!currentLevel.value) return false;

 if (countdownTime.value!=timeUsed) {
     timeUsed = totalTime.value
 }

  try {
    // 构建提交参数
    const answers = questions.slice(0, answeredQuestionCount.value) // 只取前 answeredQuestionCount 个（如果需要限制数量）
    .map(q => ({
      questionId: q.id,
      userAnswer: userAnswers[q.id] || 0,
      isCorrect: userAnswers[q.id] === q.answer,
    }));

    const res = await submitMathResult({
      level: currentLevel.value,
      answers,
      timeUsed,
    });

    // 更新结果
    Object.assign(quizResult, res);
    isQuizzing.value = false;

    return true;
  } catch (error) {
    console.error('提交答题结果失败:', error);
    return false;
  }
};

// 重置状态
export const resetQuiz = () => {
  currentLevel.value = null;
  questions.length = 0;
  currentQuestionIndex.value = 0;
  Object.keys(userAnswers).forEach(key => delete userAnswers[key]);
  countdownTime.value = 60;
  isQuizzing.value = false;

  Object.assign(quizResult, {
    totalQuestions: 0,
    correctCount: 0,
    wrongCount: 0,
    wrongQuestions: [],
    timeUsed: 0,
  });
};
