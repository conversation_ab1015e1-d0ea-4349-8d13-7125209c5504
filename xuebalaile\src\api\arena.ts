/**
 * 打擂相关API
 */
import { get } from '../utils/request';
import UNI_APP from '@/.env.js';

// WebSocket基础URL
const WS_BASE_URL = UNI_APP.WS_BASE_URL;

// 打擂级别接口
export interface ArenaLevel {
  name: string;
  value: string;
  description: string;
}

// 打擂排行榜接口
export interface ArenaRanking {
  userId: number;
  nickname: string;
  avatar: string;
  score: number;
  correctCount: number;
  rank: number;
  rankDate: string;
}

// 打擂玩家接口
export interface ArenaPlayer {
  userId: number;
  nickname: string;
  avatar: string;
  status: string;
  score: number;
  correctCount: number;
  wrongCount: number;
  rank: number;
  isOnline: boolean;
}

// 打擂题目接口
export interface ArenaQuestion {
  id: string;
  question: string;
  options: number[];
  type: string;
  level: string;
  order: number;
}

// 打擂房间接口
export interface ArenaRoom {
  roomId: string;
  status: string;
  subjectType: string;
  level: string;
  questionCount: number;
  timeLimit: number;
  startTime?: string;
  playerCount: number;
  maxPlayers: number;
  players?: ArenaPlayer[];
  countdown?: number;
}

// 打擂结果接口
export interface ArenaResult {
  roomId: string;
  level: string;
  myResult: ArenaPlayer;
  ranking: ArenaPlayer[];
  totalPlayers: number;
  finishTime: string;
}

/**
 * 获取打擂级别列表
 */
export const getArenaLevels = () => {
  return get<ArenaLevel[]>('/arena/levels');
};

/**
 * 获取今日排行榜
 */
export const getTodayRankings = (subjectType: string = 'math', level: string = '') => {
  return get<ArenaRanking[]>('/arena/rankings/today', { subjectType, level });
};

/**
 * 获取用户排行榜历史
 */
export const getUserRankings = () => {
  return get<ArenaRanking[]>('/arena/rankings/user');
};

/**
 * WebSocket连接管理
 */
class ArenaSocketManager {
  private socketTask: any = null;
  private reconnectTimer: number | null = null;
  private heartbeatTimer: number | null = null;
  private handlers: Record<string, Function[]> = {};
  private sessionId: string | null = null;
  private roomId: string | null = null;
  private reconnecting: boolean = false;
  private heartbeatInterval: number = 15000; // 15秒发送一次心跳
  private isConnected: boolean = false;
  private messageQueue: Array<{ type: string, data: any }> = [];

  // 连接WebSocket
  connect() {
    if (this.socketTask) {
      this.close();
    }

    const token = uni.getStorageSync('token') || '';
    console.log('准备连接WebSocket:', `${WS_BASE_URL}/api/arena/ws?token=${token}`);

    try {
      this.socketTask = uni.connectSocket({
        url: `${WS_BASE_URL}/api/arena/ws?token=${token}`,
        // 确保请求头中包含正确的信息
        header: {
          'content-type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        // 不设置protocols，避免兼容性问题
        success: (res) => {
          console.log('WebSocket连接请求已发送，成功回调:', res);
        },
        fail: (err) => {
          console.error('WebSocket连接失败，错误详情:', err);
          // 显示错误信息
          uni.showToast({
            title: `连接失败: ${JSON.stringify(err)}`,
            icon: 'none',
            duration: 3000
          });
          this.trigger('error', err);
        },
        complete: (res) => {
          console.log('WebSocket连接请求完成:', res);
        }
      });

      console.log('WebSocket对象创建:', this.socketTask);

      this.socketTask.onOpen((res) => {
        console.log('WebSocket连接已打开:', res);
        this.onOpen();
      });

      this.socketTask.onMessage((res: any) => {
        console.log("#### ws recv, res: ", res)
        this.onMessage(res);
      });

      this.socketTask.onError((err: any) => {
        console.error('WebSocket发生错误:', err);
        uni.showToast({
          title: `WebSocket错误: ${JSON.stringify(err)}`,
          icon: 'none',
          duration: 3000
        });
        this.onError(err);
      });

      this.socketTask.onClose((res) => {
        console.log('WebSocket连接已关闭:', res);
        this.onClose();
      });
    } catch (error) {
      console.error('创建WebSocket连接时发生异常:', error);
      uni.showToast({
        title: `连接异常: ${error}`,
        icon: 'none',
        duration: 3000
      });
    }
  }

  // 发送消息
  send(type: string, data: any) {
    if (!this.isConnected) {
      console.log("#### ws send, not connected")
      // 如果未连接，将消息加入队列
      this.messageQueue.push({ type, data });

      if (!this.reconnecting) {
        this.connect();
      }
      return;
    }

    let sentdata = JSON.stringify({ type, data });
    console.log("#### ws send, sentdata: ", sentdata)
    this.socketTask.send({
      data: sentdata,
      fail: (err: any) => {
        console.error('发送消息失败:', err);
        // 发送失败时重新连接
        this.isConnected = false;
        this.messageQueue.push({ type, data });
        this.connect();
      },
    });
  }

  // 设置会话ID
  setSessionId(sessionId: string) {
    this.sessionId = sessionId;
    // 保存到本地存储
    uni.setStorageSync('arena_session_id', sessionId);
  }

  // 设置房间ID
  setRoomId(roomId: string) {
    this.roomId = roomId;
    // 保存到本地存储
    uni.setStorageSync('arena_room_id', roomId);
  }

  // 尝试重连
  tryReconnect() {
    if (this.reconnecting) return;

    this.reconnecting = true;
    console.log('尝试重新连接...');

    // 连接WebSocket
    this.connect();

    // 连接成功后发送重连请求
    this.once('open', () => {
      const sessionId = this.sessionId || uni.getStorageSync('arena_session_id');
      if (sessionId) {
        this.send('reconnect', { sessionId });
      } else {
        this.reconnecting = false;
        this.trigger('reconnect_failed', { reason: 'No session ID' });
      }
    });
  }

  // 开始心跳
  startHeartbeat() {
    this.stopHeartbeat();

    this.heartbeatTimer = setInterval(() => {
      this.send('ping', { timestamp: Date.now() });
    }, this.heartbeatInterval) as unknown as number;
  }

  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  // 注册事件处理器
  on(type: string, handler: Function) {
    if (!this.handlers[type]) {
      this.handlers[type] = [];
    }
    this.handlers[type].push(handler);
  }

  // 注册一次性事件处理器
  once(type: string, handler: Function) {
    const onceHandler = (data: any) => {
      handler(data);
      this.off(type, onceHandler);
    };
    this.on(type, onceHandler);
  }

  // 移除事件处理器
  off(type: string, handler?: Function) {
    if (!handler) {
      delete this.handlers[type];
    } else if (this.handlers[type]) {
      this.handlers[type] = this.handlers[type].filter(h => h !== handler);
    }
  }

  // 触发事件
  trigger(type: string, data: any) {
    if (this.handlers[type]) {
      this.handlers[type].forEach(handler => handler(data));
    }
  }

  // 关闭连接
  close() {
    this.stopHeartbeat();

    if (this.socketTask) {
      this.socketTask.close({
        success: () => {
          console.log('WebSocket连接已关闭');
        },
        fail: (err: any) => {
          console.error('关闭WebSocket连接失败:', err);
        }
      });
      this.socketTask = null;
    }

    this.isConnected = false;

    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
  }

  private onOpen() {
    console.log('WebSocket连接已建立');
    this.isConnected = true;
    this.reconnecting = false;
    this.startHeartbeat();
    this.trigger('open', {});

    // 发送队列中的消息
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        this.send(message.type, message.data);
      }
    }
  }

  private onMessage(res: any) {
    try {
      const message = JSON.parse(res.data);
      const { type, data } = message;

      // 处理特殊消息类型
      if (type === 'pong') {
        // 心跳响应，不需要特殊处理
        return;
      } else if (type === 'reconnect_result') {
        this.handleReconnectResult(data);
        return;
      } else if (type === 'join_room_result' && data.success) {
        // 保存会话ID和房间ID
        this.setSessionId(data.sessionId);
        this.setRoomId(data.roomId);
      }

      // 触发对应类型的事件
      this.trigger(type, data);
    } catch (error) {
      console.error('解析WebSocket消息失败:', error);
    }
  }

  private handleReconnectResult(data: any) {
    if (data.success) {
      // 重连成功
      this.setRoomId(data.roomId);
      this.trigger('reconnect_success', data);
    } else {
      // 重连失败
      this.sessionId = null;
      this.roomId = null;
      uni.removeStorageSync('arena_session_id');
      uni.removeStorageSync('arena_room_id');
      this.trigger('reconnect_failed', data);
    }

    this.reconnecting = false;
  }

  private onError(err: any) {
    console.error('WebSocket错误详情:', JSON.stringify(err));

    // 记录详细的错误信息
    let errorMsg = '';
    if (err.errMsg) {
      errorMsg = err.errMsg;
    } else if (typeof err === 'string') {
      errorMsg = err;
    } else {
      errorMsg = JSON.stringify(err);
    }

    console.error('WebSocket错误消息:', errorMsg);

    // 显示错误提示
    uni.showToast({
      title: `WebSocket错误: ${errorMsg}`,
      icon: 'none',
      duration: 3000
    });

    this.isConnected = false;
    this.trigger('error', err);

    // 如果是HTTP状态错误，可能是服务器配置问题
    if (errorMsg.includes('Invalid HTTP status')) {
      console.error('WebSocket连接失败，可能是服务器配置问题或URL错误');
      uni.showModal({
        title: 'WebSocket连接失败',
        content: '连接服务器失败，请检查网络或联系管理员',
        showCancel: false
      });
    }
  }

  private onClose() {
    console.log('WebSocket连接已关闭');
    this.isConnected = false;
    this.stopHeartbeat();

    if (!this.reconnecting) {
      // 自动重连
      this.reconnectTimer = setTimeout(() => {
        this.tryReconnect();
      }, 3000) as unknown as number;
    }

    this.trigger('close', {});
  }
}

export const arenaSocket = new ArenaSocketManager();
