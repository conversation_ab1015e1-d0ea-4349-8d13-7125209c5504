<template>
  <view class="container">
    <view class="header">
      <view class="timer">
        <text class="timer-label">倒计时：</text>
        <text class="timer-value" :class="{ 'timer-warning': battleState.timeRemaining <= 10 }">
          {{ formatTime(battleState.timeRemaining) }}
        </text>
      </view>
      <view class="progress">
        <text class="progress-text">{{ battleState.currentIndex + 1 }}/{{ battleState.questions.length }}</text>
      </view>
    </view>

    <view class="question-area">
      <view class="question-card">
        <!-- 调试信息 -->
        <view style="background: #f0f0f0; padding: 10rpx; margin-bottom: 20rpx; font-size: 24rpx;">
          <text>调试信息:</text><br/>
          <text>currentQuestion存在: {{ !!currentQuestion }}</text><br/>
          <text>question字段: {{ currentQuestion?.question || '无' }}</text><br/>
          <text>options字段: {{ currentQuestion?.options || '无' }}</text><br/>
          <text>questions长度: {{ battleState.questions.length }}</text><br/>
          <text>currentIndex: {{ battleState.currentIndex }}</text><br/>
          <button @click="testQuestionData" style="margin-top: 10rpx; padding: 5rpx 10rpx; background: #007aff; color: white; border: none; border-radius: 4rpx;">
            测试题目数据
          </button>
        </view>

        <!-- 题目显示 -->
        <view v-if="currentQuestion && currentQuestion.question">
          <text class="question-text">{{ currentQuestion.question }}</text>
          <view class="options" v-if="currentQuestion.options && currentQuestion.options.length > 0">
            <view
              v-for="(option, index) in currentQuestion.options"
              :key="index"
              class="option-item"
              @click="selectOption(index)"
            >
              <view class="option-letter">{{ ['A', 'B', 'C', 'D'][index] }}</view>
              <text class="option-text">{{ option }}</text>
            </view>
          </view>
          <view v-else style="text-align: center; color: #999; margin-top: 20rpx;">
            <text>选项加载中...</text>
          </view>
        </view>

        <!-- 如果没有题目，显示加载状态 -->
        <view v-else style="text-align: center; color: #999; margin-top: 40rpx;">
          <text>题目加载中...</text>
        </view>
      </view>
    </view>

    <view class="ranking-area">
      <text class="ranking-title">实时排名</text>
      <view class="ranking-list">
        <view
          v-for="(player, index) in sortedPlayers"
          :key="player.userId"
          class="ranking-item"
          :class="{ 'current-user': player.userId === currentUserId }"
        >
          <text class="ranking-index">{{ index + 1 }}</text>
          <image class="ranking-avatar" :src="player.avatar" mode="aspectFill" />
          <view class="ranking-info">
            <text class="ranking-name">{{ player.nickname }}</text>
            <view class="ranking-progress">
              <view
                class="ranking-progress-bar"
                :style="{ width: (player.progress?.currentIndex / battleState.questions.length) * 100 + '%' }"
              ></view>
            </view>
          </view>
          <text class="ranking-score">{{ player.progress?.score || 0 }}分</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { battleState, formatTime, submitAnswer } from '../../store/arena';
import { arenaSocket } from '../../api/arena';
import { userInfo } from '../../store/user';

// 当前用户ID
const currentUserId = ref(userInfo.id);

// 当前题目
const currentQuestion = computed(() => {
  console.log('计算当前题目:', battleState.currentQuestion);
  return battleState.currentQuestion;
});

// 玩家列表
const players = ref<any[]>([]);

// 排序后的玩家列表
const sortedPlayers = computed(() => {
  return [...players.value].sort((a, b) => {
    // 按得分降序排序
    const scoreA = a.progress?.score || 0;
    const scoreB = b.progress?.score || 0;
    if (scoreA !== scoreB) {
      return scoreB - scoreA;
    }

    // 得分相同，按进度降序排序
    const progressA = a.progress?.currentIndex || 0;
    const progressB = b.progress?.currentIndex || 0;
    return progressB - progressA;
  });
});

// 测试函数：模拟题目数据
const testQuestionData = () => {
  console.log('开始测试题目数据...');

  // 模拟后端发送的数据
  const mockGameStartData = {
    questions: [
      {
        id: "test-question-1",
        question: "5 + 3 = ?",
        options: [8, 7, 9, 6],
        type: "addition",
        level: "level10",
        order: 0
      },
      {
        id: "test-question-2",
        question: "10 - 4 = ?",
        options: [6, 5, 7, 8],
        type: "subtraction",
        level: "level10",
        order: 1
      }
    ],
    timeLimit: 60
  };

  console.log('模拟数据:', JSON.stringify(mockGameStartData, null, 2));

  // 直接触发game_start事件
  arenaSocket.trigger('game_start', mockGameStartData);
};

// 页面加载时
onMounted(() => {
  // 监听玩家进度更新
  arenaSocket.on('player_progress', (data) => {
    updatePlayerProgress(data.progress);
  });

  // 监听游戏结果
  arenaSocket.on('game_result', () => {
    // 跳转到结果页面
    uni.navigateTo({ url: '/pages/arena/result' });
  });

  // 如果比赛已结束，直接跳转到结果页面
  if (battleState.isFinished) {
    uni.navigateTo({ url: '/pages/arena/result' });
  }

  // 延迟3秒后测试题目数据（用于调试）
  setTimeout(() => {
    if (battleState.questions.length === 0) {
      console.log('没有题目数据，开始测试...');
      testQuestionData();
    }
  }, 3000);
});

// 页面卸载时
onUnmounted(() => {
  // 移除事件监听
  arenaSocket.off('player_progress');
  arenaSocket.off('game_result');
});

// 监听比赛状态变化
watch(() => battleState.isFinished, (isFinished) => {
  if (isFinished) {
    // 跳转到结果页面
    uni.navigateTo({ url: '/pages/arena/result' });
  }
});

// 更新玩家进度
const updatePlayerProgress = (progress: Record<string, any>) => {
  // 更新玩家列表
  const newPlayers: any[] = [];

  for (const [userId, playerProgress] of Object.entries(progress)) {
    // 查找玩家
    const player = players.value.find(p => p.userId === parseInt(userId));

    if (player) {
      // 更新玩家进度
      player.progress = playerProgress;
      newPlayers.push(player);
    } else {
      // 添加新玩家
      newPlayers.push({
        userId: parseInt(userId),
        nickname: playerProgress.nickname || '未知玩家',
        avatar: playerProgress.avatar || '',
        progress: playerProgress
      });
    }
  }

  players.value = newPlayers;
};

// 选择选项
const selectOption = (index: number) => {
  // 提交答案
  submitAnswer(index);
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #eeeeee;
}

.timer {
  display: flex;
  align-items: center;
}

.timer-label {
  font-size: 28rpx;
  color: #666666;
  margin-right: 10rpx;
}

.timer-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.timer-warning {
  color: #ff3b30;
}

.progress {
  background-color: #f0f0f0;
  padding: 6rpx 20rpx;
  border-radius: 20rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #666666;
}

.question-area {
  padding: 30rpx;
}

.question-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.question-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 40rpx;
  display: block;
  text-align: center;
}

.options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f5f7fa;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
}

.option-letter {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #3cc51f;
  color: #ffffff;
  font-size: 32rpx;
  font-weight: bold;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.option-text {
  font-size: 32rpx;
  color: #333333;
}

.ranking-area {
  flex: 1;
  padding: 0 30rpx 30rpx;
}

.ranking-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.ranking-list {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.ranking-item:last-child {
  border-bottom: none;
}

.current-user {
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.ranking-index {
  width: 40rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #666666;
  margin-right: 10rpx;
}

.ranking-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  margin-right: 15rpx;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 5rpx;
  display: block;
}

.ranking-progress {
  height: 10rpx;
  background-color: #f0f0f0;
  border-radius: 5rpx;
  overflow: hidden;
  width: 100%;
}

.ranking-progress-bar {
  height: 100%;
  background-color: #3cc51f;
  transition: width 0.3s;
}

.ranking-score {
  font-size: 28rpx;
  font-weight: bold;
  color: #ff6b00;
  margin-left: 15rpx;
}
</style>
