package handler

import (
	"net/http"

	"xuebalaile-api/internal/app/service"
	"xuebalaile-api/internal/pkg/math"

	"github.com/gin-gonic/gin"
)

// MathHandler 口算处理器
type MathHandler struct {
	mathService service.MathService
}

// NewMathHandler 创建口算处理器
func NewMathHandler(mathService service.MathService) *MathHandler {
	return &MathHandler{
		mathService: mathService,
	}
}

// GetQuestions 获取口算题目
func (h *MathHandler) GetQuestions(c *gin.Context) {
	levelStr := c.Query("level")
	if levelStr == "" {
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "level is required",
		})
		return
	}

	level := math.MathLevel(levelStr)
	questions, err := h.mathService.GenerateQuestions(level)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.<PERSON>rror(),
		})
		return
	}

	// 隐藏答案
	for i := range questions {
		questions[i].Answer = 0
	}

	c.<PERSON>(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    questions,
	})
}

// SubmitResult 提交答题结果
func (h *MathHandler) SubmitResult(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Unauthorized",
		})
		return
	}

	var req struct {
		Level    math.MathLevel             `json:"level" binding:"required"`
		Answers  []service.AnswerSubmission `json:"answers" binding:"required"`
		TimeUsed int                        `json:"timeUsed" binding:"required,min=0"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	result, err := h.mathService.SubmitResult(userID.(uint), req.Level, req.Answers, req.TimeUsed)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    result,
	})
}

// GetWrongQuestions 获取错题集
func (h *MathHandler) GetWrongQuestions(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Unauthorized",
		})
		return
	}

	wrongQuestions, err := h.mathService.GetWrongQuestions(userID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    wrongQuestions,
	})
}

// SubmitWrongQuestionAnswer 提交错题答案
func (h *MathHandler) SubmitWrongQuestionAnswer(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Unauthorized",
		})
		return
	}

	var req struct {
		QuestionID string `json:"questionId" binding:"required"`
		Answer     int    `json:"answer" binding:"required,min=0"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	isCorrect, correctAnswer, err := h.mathService.SubmitWrongQuestionAnswer(userID.(uint), req.QuestionID, req.Answer)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data": gin.H{
			"isCorrect":     isCorrect,
			"correctAnswer": correctAnswer,
		},
	})
}

// GetCompetitionLevels 获取练习级别列表
func (h *MathHandler) GetCompetitionLevels(c *gin.Context) {
	levels, err := h.mathService.GetCompetitionLevels()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    levels,
	})
}

// RegisterRoutes 注册路由
func (h *MathHandler) RegisterRoutes(router *gin.RouterGroup, authMiddleware gin.HandlerFunc) {
	// 不需要认证的路由
	router.GET("/competition/levels", h.GetCompetitionLevels)

	// 需要认证的路由
	authRouter := router.Group("/")
	authRouter.Use(authMiddleware)
	{
		authRouter.GET("/questions", h.GetQuestions)
		authRouter.POST("/result", h.SubmitResult)
		authRouter.GET("/wrong-questions", h.GetWrongQuestions)
		authRouter.POST("/wrong-question/answer", h.SubmitWrongQuestionAnswer)
	}
}
