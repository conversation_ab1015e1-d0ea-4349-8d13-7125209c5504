<template>
  <view class="container">
    <view class="header">
      <text class="title">Arena调试页面</text>
    </view>
    
    <view class="section">
      <text class="section-title">WebSocket连接状态</text>
      <text class="status">{{ isConnected ? '已连接' : '未连接' }}</text>
      <button @click="connectWS" :disabled="isConnected">连接WebSocket</button>
    </view>
    
    <view class="section">
      <text class="section-title">模拟题目数据</text>
      <button @click="simulateGameStart">模拟game_start消息</button>
    </view>
    
    <view class="section">
      <text class="section-title">当前题目状态</text>
      <view class="debug-info">
        <text>battleState.questions长度: {{ battleState.questions.length }}</text>
        <text>battleState.currentIndex: {{ battleState.currentIndex }}</text>
        <text>battleState.currentQuestion存在: {{ !!battleState.currentQuestion }}</text>
        <text v-if="battleState.currentQuestion">当前题目: {{ battleState.currentQuestion.question }}</text>
        <text v-if="battleState.currentQuestion">选项数量: {{ battleState.currentQuestion.options?.length || 0 }}</text>
        <text v-if="battleState.currentQuestion && battleState.currentQuestion.options">
          选项: {{ JSON.stringify(battleState.currentQuestion.options) }}
        </text>
      </view>
    </view>
    
    <view class="section">
      <text class="section-title">题目显示测试</text>
      <view class="question-card" v-if="battleState.currentQuestion">
        <text class="question-text">{{ battleState.currentQuestion.question }}</text>
        <view class="options" v-if="battleState.currentQuestion.options">
          <view
            v-for="(option, index) in battleState.currentQuestion.options"
            :key="index"
            class="option-item"
          >
            <view class="option-letter">{{ ['A', 'B', 'C', 'D'][index] }}</view>
            <text class="option-text">{{ option }}</text>
          </view>
        </view>
      </view>
      <text v-else>没有题目数据</text>
    </view>
    
    <view class="section">
      <text class="section-title">消息日志</text>
      <view class="log-container">
        <text v-for="(log, index) in logs" :key="index" class="log-item">{{ log }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { battleState } from '../../store/arena';
import { arenaSocket } from '../../api/arena';

const isConnected = ref(false);
const logs = ref<string[]>([]);

const addLog = (message: string) => {
  const timestamp = new Date().toLocaleTimeString();
  logs.value.unshift(`[${timestamp}] ${message}`);
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20);
  }
};

const connectWS = () => {
  addLog('尝试连接WebSocket...');
  arenaSocket.connect();
};

const simulateGameStart = () => {
  const mockData = {
    questions: [
      {
        id: "test-1",
        question: "3 + 5 = ?",
        options: [8, 7, 9, 6],
        type: "addition",
        level: "level10",
        order: 0
      },
      {
        id: "test-2", 
        question: "10 - 4 = ?",
        options: [6, 5, 7, 8],
        type: "subtraction",
        level: "level10",
        order: 1
      }
    ],
    timeLimit: 60
  };
  
  addLog('模拟发送game_start消息: ' + JSON.stringify(mockData));
  
  // 直接触发事件处理器
  arenaSocket.trigger('game_start', mockData);
};

onMounted(() => {
  // 监听WebSocket事件
  arenaSocket.on('open', () => {
    isConnected.value = true;
    addLog('WebSocket连接成功');
  });
  
  arenaSocket.on('close', () => {
    isConnected.value = false;
    addLog('WebSocket连接关闭');
  });
  
  arenaSocket.on('error', (error) => {
    addLog('WebSocket错误: ' + JSON.stringify(error));
  });
  
  arenaSocket.on('game_start', (data) => {
    addLog('收到game_start消息: ' + JSON.stringify(data));
  });
  
  addLog('调试页面已加载');
});

onUnmounted(() => {
  // 清理事件监听
  arenaSocket.off('open');
  arenaSocket.off('close');
  arenaSocket.off('error');
  arenaSocket.off('game_start');
});
</script>

<style>
.container {
  padding: 20rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 20rpx;
  border-radius: 8rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
  display: block;
}

.status {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.debug-info text {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
}

.question-card {
  background-color: #f5f5f5;
  padding: 20rpx;
  border-radius: 8rpx;
}

.question-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.options {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  background-color: #fff;
  border-radius: 6rpx;
}

.option-letter {
  width: 50rpx;
  height: 50rpx;
  border-radius: 25rpx;
  background-color: #007aff;
  color: #fff;
  font-size: 24rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 15rpx;
}

.option-text {
  font-size: 28rpx;
  color: #333;
}

.log-container {
  max-height: 400rpx;
  overflow-y: auto;
  background-color: #f5f5f5;
  padding: 10rpx;
  border-radius: 6rpx;
}

.log-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 5rpx;
  display: block;
  word-break: break-all;
}

button {
  background-color: #007aff;
  color: #fff;
  border: none;
  padding: 15rpx 30rpx;
  border-radius: 6rpx;
  font-size: 28rpx;
  margin-top: 10rpx;
}

button:disabled {
  background-color: #ccc;
}
</style>
