/**
 * 口算相关API
 */
import { get, post } from '../utils/request';

// 口算难度级别类型
export type MathLevel = string;

// 练习级别接口
export interface CompetitionLevel {
  name: string;
  value: MathLevel;
  description: string;
}

// 口算题目类型
export enum MathType {
  ADDITION = 'addition',
  SUBTRACTION = 'subtraction',
}

// 口算题目接口
export interface MathQuestion {
  id: string;
  question: string;
  answer: number;
  type: MathType;
  level: MathLevel;
}

// 答题结果接口
export interface MathResult {
  totalQuestions: number;
  correctCount: number;
  wrongCount: number;
  wrongQuestions: Array<{
    question: MathQuestion;
    userAnswer: number;
  }>;
  timeUsed: number; // 秒数
}

// 提交答案参数
export interface SubmitAnswerParams {
  questionId: string;
  answer: number;
}

// 提交结果参数
export interface SubmitResultParams {
  level: MathLevel;
  answers: Array<{
    questionId: string;
    userAnswer: number;
    isCorrect: boolean;
  }>;
  timeUsed: number; // 秒数
}

/**
 * 获取口算题目
 */
export const getMathQuestions = (level: MathLevel) => {
  return get<MathQuestion[]>('/math/questions', { level });
};

/**
 * 提交答题结果
 */
export const submitMathResult = (params: SubmitResultParams) => {
  return post<MathResult>('/math/result', params);
};

/**
 * 获取错题集
 */
export const getWrongQuestions = () => {
  return get<Array<{
    question: MathQuestion;
    userAnswer: number;
  }>>('/math/wrong-questions');
};

/**
 * 获取练习级别列表
 */
export const getCompetitionLevels = () => {
  return get<CompetitionLevel[]>('/math/competition/levels');
};

/**
 * 提交错题练习结果
 */
export const submitWrongQuestionAnswer = (params: SubmitAnswerParams) => {
  return post<{ isCorrect: boolean; correctAnswer: number }>('/math/wrong-question/answer', params);
};
