package model

import (
	"time"

	"gorm.io/gorm"
)

const (
	RoomStatusWaiting  = `waiting`
	RoomStatusRunning  = `running`
	RoomStatusFinished = `finished`

	PlayerStatusWaiting  = `waiting`
	PlayerStatusPlaying  = `playing`
	PlayerStatusFinished = `finished`
)

// ArenaRoom 打擂房间模型
type ArenaRoom struct {
	gorm.Model
	RoomID        string    `gorm:"size:50;uniqueIndex;not null"` // 房间唯一ID
	Status        string    `gorm:"size:20;not null"`             // waiting, running, finished
	SubjectType   string    `gorm:"size:20;not null"`             // math, english, etc.
	Level         string    `gorm:"size:50;not null"`             // 难度级别
	QuestionCount int       `gorm:"not null"`                     // 题目数量
	TimeLimit     int       `gorm:"not null"`                     // 时间限制（秒）
	StartTime     time.Time // 开始时间
	EndTime       time.Time // 结束时间
	PlayerCount   int       `gorm:"not null;default:0"`   // 当前玩家数量
	MaxPlayers    int       `gorm:"not null;default:100"` // 最大玩家数量
	ExpireAt      time.Time // 房间过期时间
	IsArchived    bool      `gorm:"default:false"` // 是否已归档
}

// ArenaPlayer 打擂玩家模型
type ArenaPlayer struct {
	gorm.Model
	RoomID          string    `gorm:"size:50;not null;index"`
	UserID          uint      `gorm:"not null;index"`
	SessionID       string    `gorm:"size:50;index"` // 会话ID，用于断线重连
	Nickname        string    `gorm:"size:50;not null"`
	Avatar          string    `gorm:"size:255"`
	Status          string    `gorm:"size:20;not null"`      // waiting, playing, finished
	Score           int       `gorm:"not null;default:0"`    // 得分
	CorrectCount    int       `gorm:"not null;default:0"`    // 正确题目数
	WrongCount      int       `gorm:"not null;default:0"`    // 错误题目数
	FinishTime      int       `gorm:"not null;default:0"`    // 完成时间（毫秒）
	Rank            int       `gorm:"not null;default:0"`    // 排名
	IsOnline        bool      `gorm:"not null;default:true"` // 是否在线
	LastActiveAt    time.Time // 最后活跃时间
	LastPingTime    time.Time // 最后一次心跳时间
	DisconnectAt    time.Time // 断开连接时间
	CurrentQuestion int       `gorm:"default:0"` // 当前题目索引
}

// ArenaQuestion 打擂题目模型
type ArenaQuestion struct {
	gorm.Model
	RoomID     string `gorm:"size:50;not null;index"`
	QuestionID string `gorm:"size:50;not null"`
	Question   string `gorm:"size:100;not null"`
	Answer     int    `gorm:"not null"`
	OptionA    int    `gorm:"not null"`         // 选项A
	OptionB    int    `gorm:"not null"`         // 选项B
	OptionC    int    `gorm:"not null"`         // 选项C
	OptionD    int    `gorm:"not null"`         // 选项D
	Type       string `gorm:"size:20;not null"` // 题目类型
	Level      string `gorm:"size:50;not null"` // 难度级别
	Order      int    `gorm:"not null"`         // 题目顺序
}

// ArenaAnswer 打擂答案模型
type ArenaAnswer struct {
	gorm.Model
	RoomID     string `gorm:"size:50;not null;index"`
	UserID     uint   `gorm:"not null;index"`
	QuestionID string `gorm:"size:50;not null"`
	UserAnswer int    `gorm:"not null"` // 用户选择的选项(0-3对应A-D)
	IsCorrect  bool   `gorm:"not null"`
	AnswerTime int    `gorm:"not null"` // 答题时间（毫秒）
}

// ArenaRanking 打擂排行榜模型
type ArenaRanking struct {
	gorm.Model
	UserID       uint      `gorm:"not null;index"`
	Nickname     string    `gorm:"size:50;not null"`
	Avatar       string    `gorm:"size:255"`
	SubjectType  string    `gorm:"size:20;not null;index"` // math, english, etc.
	Level        string    `gorm:"size:50;not null;index"` // 难度级别
	Score        int       `gorm:"not null"`               // 得分
	CorrectCount int       `gorm:"not null"`               // 正确题目数
	WrongCount   int       `gorm:"not null"`               // 错误题目数
	FinishTime   int       `gorm:"not null"`               // 完成时间（毫秒）
	Rank         int       `gorm:"not null"`               // 排名
	RankDate     time.Time `gorm:"index"`                  // 排名日期
}

// ArenaRoomResponse 打擂房间响应
type ArenaRoomResponse struct {
	RoomID        string                `json:"roomId"`
	Status        string                `json:"status"`
	SubjectType   string                `json:"subjectType"`
	Level         string                `json:"level"`
	QuestionCount int                   `json:"questionCount"`
	TimeLimit     int                   `json:"timeLimit"`
	StartTime     time.Time             `json:"startTime,omitempty"`
	PlayerCount   int                   `json:"playerCount"`
	MaxPlayers    int                   `json:"maxPlayers"`
	Players       []ArenaPlayerResponse `json:"players,omitempty"`
	Countdown     int                   `json:"countdown,omitempty"`
}

// ArenaPlayerResponse 打擂玩家响应
type ArenaPlayerResponse struct {
	UserID       uint   `json:"userId"`
	Nickname     string `json:"nickname"`
	Avatar       string `json:"avatar"`
	Status       string `json:"status"`
	Score        int    `json:"score,omitempty"`
	CorrectCount int    `json:"correctCount,omitempty"`
	WrongCount   int    `json:"wrongCount,omitempty"`
	Rank         int    `json:"rank,omitempty"`
	IsOnline     bool   `json:"isOnline"`
}

// ArenaQuestionResponse 打擂题目响应
type ArenaQuestionResponse struct {
	ID       string `json:"id"`
	Question string `json:"question"`
	Options  []int  `json:"options"`
	Type     string `json:"type"`
	Level    string `json:"level"`
	Order    int    `json:"order"`
}

// ArenaResultResponse 打擂结果响应
type ArenaResultResponse struct {
	RoomID       string                `json:"roomId"`
	Level        string                `json:"level"`
	MyResult     ArenaPlayerResponse   `json:"myResult"`
	Ranking      []ArenaPlayerResponse `json:"ranking"`
	TotalPlayers int                   `json:"totalPlayers"`
	FinishTime   time.Time             `json:"finishTime"`
}

// ArenaRankingResponse 打擂排行榜响应
type ArenaRankingResponse struct {
	UserID       uint   `json:"userId"`
	Nickname     string `json:"nickname"`
	Avatar       string `json:"avatar"`
	Score        int    `json:"score"`
	CorrectCount int    `json:"correctCount"`
	Rank         int    `json:"rank"`
	RankDate     string `json:"rankDate"`
}
