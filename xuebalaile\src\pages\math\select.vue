<template>
  <view class="container">
    <view class="header">
      <text class="title">练习内容</text>
      <text class="subtitle">选择难度开始挑战</text>
    </view>

    <view class="level-list">
      <view v-if="levels.length === 0" class="loading-container">
        <text class="loading-text">加载练习级别中...</text>
      </view>
      <view v-else v-for="(level, index) in levels" :key="index" class="level-item" @click="selectLevel(level.value)">
        <view class="level-info">
          <text class="level-name">{{ level.name }}</text>
          <text class="level-desc">{{ level.description }}</text>
        </view>
        <text class="level-icon">></text>
      </view>
    </view>

    <view class="rules">
      <text class="rules-title">练习规则</text>
      <view class="rules-content">
        <text class="rule-item">1. 选择难度后开始答题</text>
        <text class="rule-item">2. 答题时间为1分钟</text>
        <text class="rule-item">3. 答对一题得1分</text>
        <text class="rule-item">4. 答错的题目会自动加入错题集</text>
        <text class="rule-item">5. 答题结束后可以练习错题</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app';
import { onShow } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { getCompetitionLevels } from '../../api/math';
import type { MathLevel, CompetitionLevel } from '../../api/math';
import { setLevel, fetchQuestions } from '../../store/math';
import { isLoggedIn, initUserState } from '../../store/user';

// 练习级别列表
const levels = ref<CompetitionLevel[]>([]);

// 获取练习级别列表
const fetchCompetitionLevels = async () => {
  try {
    uni.showLoading({ title: '加载中...' });
    const res = await getCompetitionLevels();
    levels.value = res;
    uni.hideLoading();
  } catch (error) {
    console.error('获取练习级别列表失败:', error);
    uni.showToast({
      title: '获取练习级别列表失败，请重试',
      icon: 'none'
    });
    uni.hideLoading();
  }
};

// 页面加载时检查登录状态并获取练习级别列表
onLoad(() => {
  checkLoginStatus();
  fetchCompetitionLevels();
});

// 页面显示时检查登录状态
onShow(() => {
  checkLoginStatus();
});

// 检查登录状态
const checkLoginStatus = () => {
  const loggedIn = initUserState();

  if (!loggedIn) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再参与口算小比赛',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({ url: '/pages/login/login' });
        }
      }
    });
  }
};

// 选择难度级别
const selectLevel = async (level: MathLevel) => {
  if (!isLoggedIn.value) {
    uni.showModal({
      title: '提示',
      content: '请先登录后再参与练习',
      confirmText: '去登录',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          uni.navigateTo({ url: '/pages/login/login' });
        }
      }
    });
    return;
  }

  // 设置难度级别
  setLevel(level);

  // 显示加载中
  uni.showLoading({ title: '准备题目中...' });

  // 获取题目
  const success = await fetchQuestions();

  // 隐藏加载中
  uni.hideLoading();

  if (success) {
    // 跳转到答题页面
    uni.navigateTo({ url: '/pages/math/quiz' });
  } else {
    uni.showToast({
      title: '获取题目失败，请重试',
      icon: 'none'
    });
  }
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

.header {
  padding: 40rpx 30rpx;
  background-color: #3cc51f;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.level-list {
  padding: 20rpx 30rpx;
}

.level-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.level-info {
  flex: 1;
}

.level-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.level-desc {
  font-size: 26rpx;
  color: #999999;
}

.level-icon {
  font-size: 36rpx;
  color: #cccccc;
}

.rules {
  margin-top: 20rpx;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.rules-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
  display: block;
}

.rules-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.rule-item {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.6;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  background-color: #ffffff;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.loading-text {
  font-size: 28rpx;
  color: #999999;
}
</style>
