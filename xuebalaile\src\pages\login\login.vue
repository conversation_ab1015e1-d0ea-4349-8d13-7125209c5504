<template>
  <view class="login-container">
    <view class="logo-area">
      <image class="logo" src="/static/images/logo.png" mode="aspectFit" />
      <text class="app-name">学霸来了</text>
    </view>

    <view class="login-form">
      <text class="login-title">授权登录</text>
      <text class="login-desc">请授权登录以使用完整功能</text>

      <view class="login-buttons">
        <button class="wechat-btn" open-type="getUserInfo" @click="handleWechatLogin">
          <text class="btn-text">微信登录</text>
        </button>

<!--        <button class="xiaohongshu-btn" @click="handleXiaohongshuLogin">
          <text class="btn-text">小红书登录</text>
        </button> -->
      </view>
    </view>

    <view class="privacy-policy">
      <text class="policy-text">登录即表示您同意</text>
      <text class="policy-link" @click="showPrivacyPolicy">《用户协议和隐私政策》</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { login } from '../../store/user';

// 登录状态
const isLoggingIn = ref(false);

// 微信登录
const handleWechatLogin = async () => {
  if (isLoggingIn.value) return;
  isLoggingIn.value = true;

  try {
    // 获取微信登录code
    const { code } = await uni.login({ provider: 'weixin' });
    console.log("handleWechatLogin, code:  ", code)

    // 调用登录接口
    const success = await login(code, 'wechat');

    if (success) {
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 延迟返回，让用户看到提示
      setTimeout(() => {
        uni.switchTab({ url: '/pages/index/index' });
      }, 1500);
    } else {
      uni.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('微信登录失败:', error);
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none'
    });
  } finally {
    isLoggingIn.value = false;
  }
};

// 小红书登录
const handleXiaohongshuLogin = async () => {
  if (isLoggingIn.value) return;
  isLoggingIn.value = true;
 
  try {
    // 获取小红书登录code
    const { code } = await uni.login({ provider: 'xiaohongshu' });

    // 调用登录接口
    const success = await login(code, 'xiaohongshu');

    if (success) {
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 延迟返回，让用户看到提示
      setTimeout(() => {
        uni.switchTab({ url: '/pages/index/index' });
      }, 1500);
    } else {
      uni.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
    }
  } catch (error) {
    console.error('小红书登录失败:', error);
    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none'
    });
  } finally {
    isLoggingIn.value = false;
  }
};

// 显示隐私政策
const showPrivacyPolicy = () => {
  uni.navigateTo({ url: '/pages/privacy/privacy' });
};
</script>

<style>
.login-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  padding: 40rpx;
  background-color: #ffffff;
}

.logo-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 80rpx;
  margin-bottom: 60rpx;
}

.logo {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 20rpx;
}

.app-name {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
}

.login-form {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.login-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.login-desc {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 60rpx;
}

.login-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.wechat-btn {
  height: 90rpx;
  background-color: #07c160;
  color: #ffffff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.xiaohongshu-btn {
  height: 90rpx;
  background-color: #fe2c55;
  color: #ffffff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-text {
  font-size: 32rpx;
}

.privacy-policy {
  margin-top: auto;
  text-align: center;
  padding: 40rpx 0;
}

.policy-text {
  font-size: 26rpx;
  color: #999999;
}

.policy-link {
  font-size: 26rpx;
  color: #3cc51f;
}
</style>
