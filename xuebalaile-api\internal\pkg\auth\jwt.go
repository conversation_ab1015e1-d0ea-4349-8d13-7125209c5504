package auth

import (
	"errors"
	"fmt"
	"time"

	"xuebalaile-api/internal/pkg/config"

	"github.com/golang-jwt/jwt/v5"
)

// Claims JWT声明
type Claims struct {
	UserID   uint   `json:"userId"`
	Platform string `json:"platform"`
	jwt.RegisteredClaims
}

// JWTService JWT服务
type JWTService struct {
	secretKey     string
	tokenDuration time.Duration
}

// NewJWTService 创建JWT服务
func NewJWTService(cfg *config.Config) *JWTService {
	return &JWTService{
		secretKey:     cfg.JWT.Secret,
		tokenDuration: cfg.JWT.Expiration,
	}
}

// GenerateToken 生成JWT令牌
func (s *JWTService) GenerateToken(userID uint, platform string) (string, *time.Time, *time.Time, error) {
	expiresAt := time.Now().Add(s.tokenDuration)
	notBefore := time.Now()
	claims := &Claims{
		UserID:   userID,
		Platform: platform,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(expiresAt),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signedToken, err := token.SignedString([]byte(s.secretKey))
	if err != nil {
		return "", nil, nil, fmt.Errorf("failed to sign token: %w", err)
	}

	return signedToken, &expiresAt, &notBefore, nil
}

// ValidateToken 验证JWT令牌
func (s *JWTService) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(
		tokenString,
		&Claims{},
		func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return []byte(s.secretKey), nil
		},
	)

	if err != nil {
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, errors.New("invalid token claims")
	}

	return claims, nil
}
