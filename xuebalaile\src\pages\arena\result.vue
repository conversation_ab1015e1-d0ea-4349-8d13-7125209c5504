<template>
  <view class="container">
    <view class="result-card">
      <view class="result-header">
        <text class="result-title">比赛结束</text>
        <text class="result-subtitle">{{ resultState.totalPlayers }}人参与</text>
      </view>

      <view v-if="resultState.myResult" class="my-result">
        <view class="my-rank" :class="getTopClass(resultState.myResult.rank)">
          <text class="rank-number">{{ resultState.myResult.rank }}</text>
        </view>
        <view class="my-info">
          <text class="my-score">{{ resultState.myResult.score }}分</text>
          <text class="my-stats">答对{{ resultState.myResult.correctCount }}题，答错{{ resultState.myResult.wrongCount }}题</text>
        </view>
      </view>

      <view class="divider"></view>

      <view class="ranking-section">
        <text class="section-title">排行榜</text>
        <view class="ranking-list">
          <view
            v-for="(player, index) in resultState.ranking"
            :key="player.userId"
            class="ranking-item"
            :class="{ 'current-user': player.userId === currentUserId }"
          >
            <view class="ranking-rank" :class="getTopClass(player.rank)">
              <text>{{ player.rank }}</text>
            </view>
            <image class="ranking-avatar" :src="player.avatar" mode="aspectFill" />
            <view class="ranking-info">
              <text class="ranking-name">{{ player.nickname }}</text>
              <text class="ranking-stats">答对{{ player.correctCount }}题，答错{{ player.wrongCount }}题</text>
            </view>
            <text class="ranking-score">{{ player.score }}分</text>
          </view>
        </view>
      </view>
    </view>

    <view class="action-buttons">
      <button class="action-btn retry" @click="playAgain">再来一局</button>
      <button class="action-btn home" @click="goToHome">返回首页</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { resultState, cleanupArena } from '../../store/arena';
import { arenaSocket } from '../../api/arena';
import { userInfo } from '../../store/user';

// 当前用户ID
const currentUserId = ref(userInfo.id);

// 页面加载时
onMounted(() => {
  // 如果没有结果数据，返回打擂首页
  if (resultState.ranking.length === 0) {
    uni.showToast({
      title: '没有比赛结果',
      icon: 'none'
    });
    
    setTimeout(() => {
      uni.switchTab({ url: '/pages/arena/index' });
    }, 1500);
  }
});

// 获取排名样式
const getTopClass = (rank: number) => {
  if (rank === 1) return 'rank-first';
  if (rank === 2) return 'rank-second';
  if (rank === 3) return 'rank-third';
  return '';
};

// 再来一局
const playAgain = () => {
  // 清理资源
  cleanupArena();
  
  // 关闭WebSocket连接
  arenaSocket.close();
  
  // 返回打擂首页
  uni.switchTab({ url: '/pages/arena/index' });
};

// 返回首页
const goToHome = () => {
  // 清理资源
  cleanupArena();
  
  // 关闭WebSocket连接
  arenaSocket.close();
  
  // 返回首页
  uni.switchTab({ url: '/pages/index/index' });
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 30rpx;
}

.result-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}

.result-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.result-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.result-subtitle {
  font-size: 28rpx;
  color: #999999;
}

.my-result {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  margin-bottom: 30rpx;
}

.my-rank {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50rpx;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 30rpx;
}

.rank-number {
  font-size: 48rpx;
  font-weight: bold;
  color: #666666;
}

.rank-first {
  background-color: #ffd700;
}

.rank-first .rank-number {
  color: #ffffff;
}

.rank-second {
  background-color: #c0c0c0;
}

.rank-second .rank-number {
  color: #ffffff;
}

.rank-third {
  background-color: #cd7f32;
}

.rank-third .rank-number {
  color: #ffffff;
}

.my-info {
  flex: 1;
}

.my-score {
  font-size: 48rpx;
  font-weight: bold;
  color: #ff6b00;
  margin-bottom: 10rpx;
  display: block;
}

.my-stats {
  font-size: 28rpx;
  color: #666666;
}

.divider {
  height: 2rpx;
  background-color: #eeeeee;
  margin: 20rpx 0 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
  display: block;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.current-user {
  background-color: #f0f7ff;
  border: 1rpx solid #d0e6ff;
}

.ranking-rank {
  width: 60rpx;
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-weight: bold;
  color: #666666;
  margin-right: 20rpx;
}

.ranking-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  margin-right: 20rpx;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 5rpx;
  display: block;
}

.ranking-stats {
  font-size: 24rpx;
  color: #999999;
}

.ranking-score {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b00;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 90rpx;
  line-height: 90rpx;
  font-size: 30rpx;
  border-radius: 45rpx;
}

.retry {
  background-color: #3cc51f;
  color: #ffffff;
}

.home {
  background-color: #f5f5f5;
  color: #666666;
}
</style>
