package repository

import (
	"errors"

	"xuebalaile-api/internal/app/model"
	"xuebalaile-api/internal/pkg/math"

	"gorm.io/gorm"
)

// MathRepository 口算存储库接口
type MathRepository interface {
	SaveQuestions(questions []math.Question) error
	FindQuestionByID(id string) (*model.MathQuestion, error)
	SaveResult(result *model.MathResult) error
	SaveWrongQuestions(wrongQuestions []*model.WrongQuestion) error
	GetWrongQuestionsByUserID(userID uint) ([]*model.WrongQuestion, error)
	UpdateWrongQuestion(wrongQuestion *model.WrongQuestion) error
}

// mathRepository 口算存储库实现
type mathRepository struct {
	db *gorm.DB
}

// NewMathRepository 创建口算存储库
func NewMathRepository(db *gorm.DB) MathRepository {
	return &mathRepository{db: db}
}

// SaveQuestions 保存口算题目
func (r *mathRepository) SaveQuestions(questions []math.Question) error {
	for _, q := range questions {
		mathQuestion := &model.MathQuestion{
			QuestionID: q.ID,
			Question:   q.Question,
			Answer:     q.Answer,
			Type:       string(q.Type),
			Level:      string(q.Level),
		}
		if err := r.db.Create(mathQuestion).Error; err != nil {
			return err
		}
	}
	return nil
}

// FindQuestionByID 根据ID查找口算题目
func (r *mathRepository) FindQuestionByID(id string) (*model.MathQuestion, error) {
	var question model.MathQuestion
	if err := r.db.Where("question_id = ?", id).First(&question).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &question, nil
}

// SaveResult 保存口算结果
func (r *mathRepository) SaveResult(result *model.MathResult) error {
	return r.db.Create(result).Error
}

// SaveWrongQuestions 保存错题
func (r *mathRepository) SaveWrongQuestions(wrongQuestions []*model.WrongQuestion) error {
	return r.db.Create(wrongQuestions).Error
}

// GetWrongQuestionsByUserID 获取用户的错题
func (r *mathRepository) GetWrongQuestionsByUserID(userID uint) ([]*model.WrongQuestion, error) {
	var wrongQuestions []*model.WrongQuestion
	if err := r.db.Where("user_id = ?", userID).Find(&wrongQuestions).Error; err != nil {
		return nil, err
	}
	return wrongQuestions, nil
}

// UpdateWrongQuestion 更新错题
func (r *mathRepository) UpdateWrongQuestion(wrongQuestion *model.WrongQuestion) error {
	return r.db.Save(wrongQuestion).Error
}
