<template>
  <view class="container">
    <view class="header">
      <text class="title">错题练习</text>
      <text class="subtitle">巩固知识点，提高正确率</text>
    </view>


      <view class="action-area">
        <button v-if="!showResult" class="submit-btn" @click="submitAnswer" :disabled="!answer">提交答案</button>
        <button v-else class="next-btn" @click="nextQuestion">{{ hasNextQuestion ? '下一题' : '完成练习' }}</button>
      </view>
      
    <view v-if="currentQuestion" class="question-area">
      <view class="question-card">
        <text class="question-text">{{ currentQuestion.question.question }}</text>
        <view class="answer-input">
          <input type="number" v-model="answer" placeholder="请输入答案" :focus="inputFocus" @blur="handleInputBlur"
            @confirm="submitAnswer" />
        </view>

        <view v-if="showResult" class="result-feedback" :class="{ 'correct': isCorrect, 'wrong': !isCorrect }">
          <text class="feedback-text">{{ isCorrect ? '回答正确！' : '回答错误！' }}</text>
          <text v-if="!isCorrect" class="correct-answer">正确答案: {{ currentQuestion.question.answer }}</text>
        </view>
      </view>
      
    </view>

    <view v-else class="empty-state">
      <image class="empty-icon" src="/static/images/empty.png" mode="aspectFit" />
      <text class="empty-text">暂无错题</text>
      <button class="back-btn" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { quizResult } from '../../store/math';
import { submitWrongQuestionAnswer } from '../../api/math';

// 当前题目索引
const currentIndex = ref(0);

// 当前答案
const answer = ref('');

// 输入框焦点
const inputFocus = ref(true);

// 是否显示结果
const showResult = ref(false);

// 是否回答正确
const isCorrect = ref(false);

// 当前题目
const currentQuestion = computed(() => {
  if (quizResult.wrongQuestions.length === 0 || currentIndex.value >= quizResult.wrongQuestions.length) {
    return null;
  }

  return quizResult.wrongQuestions[currentIndex.value];
});

// 是否有下一题
const hasNextQuestion = computed(() => {
  return currentIndex.value < quizResult.wrongQuestions.length - 1;
});

// 提交答案
const submitAnswer = async () => {
  if (!answer.value || !currentQuestion.value) return;

  try {
    // 调用API提交答案
    const result = await submitWrongQuestionAnswer({
      questionId: currentQuestion.value.question.id,
      answer: Number(answer.value)
    });

    // 显示结果
    isCorrect.value = result.isCorrect;
    showResult.value = true;
  } catch (error) {
    console.error('提交答案失败:', error);
    uni.showToast({
      title: '提交答案失败，请重试',
      icon: 'none'
    });
  }
};

// 下一题
const nextQuestion = () => {
  if (hasNextQuestion.value) {
    // 移动到下一题
    currentIndex.value++;

    // 重置状态
    answer.value = '';
    showResult.value = false;
    isCorrect.value = false;

    // 聚焦输入框
    inputFocus.value = false;
    setTimeout(() => {
      inputFocus.value = true;
    }, 100);
  } else {
    // 完成练习
    uni.showToast({
      title: '练习完成',
      icon: 'success'
    });

    // 返回结果页面
    setTimeout(() => {
      goBack();
    }, 1500);
  }
};

// 处理输入框失焦
const handleInputBlur = () => {
  // 延迟重新聚焦
  if (!showResult.value) {
    setTimeout(() => {
      inputFocus.value = true;
    }, 100);
  }
};

// 返回上一页
const goBack = () => {
  uni.navigateBack();
};

// 页面加载时
onMounted(() => {
  // 检查是否有错题
  if (quizResult.wrongQuestions.length === 0) {
    uni.showToast({
      title: '暂无错题',
      icon: 'none'
    });
  } else {
    // 聚焦输入框
    inputFocus.value = true;
  }
});
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 40rpx 30rpx;
  background-color: #3cc51f;
}

.title {
  font-size: 40rpx;
  font-weight: bold;
  color: #ffffff;
  margin-bottom: 10rpx;
  display: block;
}

.subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.question-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 40rpx 30rpx;
}

.question-card {
  width: 100%;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30rpx;
}

.question-text {
  font-size: 60rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 60rpx;
}

.answer-input {
  width: 80%;
  border-bottom: 2rpx solid #dddddd;
  padding: 20rpx 0;
  margin-bottom: 40rpx;
}

.answer-input input {
  width: 100%;
  height: 80rpx;
  font-size: 48rpx;
  text-align: center;
}

.result-feedback {
  width: 100%;
  padding: 20rpx;
  border-radius: 8rpx;
  text-align: center;
}

.correct {
  background-color: rgba(76, 217, 100, 0.1);
}

.wrong {
  background-color: rgba(255, 59, 48, 0.1);
}

.feedback-text {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
  display: block;
}

.correct .feedback-text {
  color: #4cd964;
}

.wrong .feedback-text {
  color: #ff3b30;
}

.correct-answer {
  font-size: 28rpx;
  color: #333333;
}

.action-area {
  margin-top: auto;
}

.submit-btn,
.next-btn {
  height: 90rpx;
  background-color: #3cc51f;
  color: #ffffff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.submit-btn[disabled] {
  background-color: #cccccc;
  color: #ffffff;
}

.next-btn {
  background-color: #007aff;
}

.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 40rpx;
}

.back-btn {
  width: 300rpx;
  height: 80rpx;
  background-color: #f0f0f0;
  color: #333333;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30rpx;
}
</style>
