package service

import (
	"errors"
	"math/rand"
	"sort"
	"time"

	"xuebalaile-api/internal/app/model"
	"xuebalaile-api/internal/app/repository"
	"xuebalaile-api/internal/pkg/math"

	"github.com/google/uuid"
)

// ArenaService 打擂服务接口
type ArenaService interface {
	// 房间相关
	FindOrCreateRoom(level string) (*model.ArenaRoom, error)
	GetRoomInfo(roomID string) (*model.ArenaRoomResponse, error)
	StartRoom(roomID string) error
	FinishRoom(roomID string) error

	// 玩家相关
	AddPlayerToRoom(roomID string, userID uint, nickname string, avatar string) (*model.ArenaPlayer, string, error)
	GetPlayerInfo(playerID uint) (*model.ArenaPlayerResponse, error)
	UpdatePlayerStatus(playerID uint, status string) error
	UpdatePlayerOnlineStatus(playerID uint, isOnline bool) error
	GetRoomPlayers(roomID string) ([]model.ArenaPlayerResponse, error)

	// 题目相关
	GenerateRoomQuestions(roomID string, level string, count int) ([]*model.ArenaQuestionResponse, error)
	GetRoomQuestions(roomID string) ([]*model.ArenaQuestionResponse, error)

	// 答案相关
	SubmitAnswer(roomID string, userID uint, questionID string, answer int, answerTime int) (bool, error)
	CalculateResults(roomID string) error

	// 排行榜相关
	GetTodayTopRankings(subjectType string, level string, limit int) ([]model.ArenaRankingResponse, error)
	GetUserRankingHistory(userID uint, limit int) ([]model.ArenaRankingResponse, error)

	// 级别相关
	GetCompetitionLevels() ([]map[string]string, error)
}

// arenaService 打擂服务实现
type arenaService struct {
	arenaRepo     repository.ArenaRepository
	mathGenerator *math.Generator
}

// NewArenaService 创建打擂服务
func NewArenaService(arenaRepo repository.ArenaRepository) ArenaService {
	return &arenaService{
		arenaRepo:     arenaRepo,
		mathGenerator: math.NewGenerator(),
	}
}

// GetCompetitionLevels 获取比赛级别
func (s *arenaService) GetCompetitionLevels() ([]map[string]string, error) {
	// 返回预定义的级别列表
	levels := []map[string]string{
		{
			"name":        "初级",
			"value":       "easy",
			"description": "适合1-2年级学生，简单的加减法",
		},
		{
			"name":        "中级",
			"value":       "medium",
			"description": "适合3-4年级学生，包含进位的加减法",
		},
		{
			"name":        "高级",
			"value":       "hard",
			"description": "适合5-6年级学生，包含乘除法",
		},
	}

	return levels, nil
}

// FindOrCreateRoom 查找或创建房间
func (s *arenaService) FindOrCreateRoom(level string) (*model.ArenaRoom, error) {
	// 查找等待中的房间
	rooms, err := s.arenaRepo.FindWaitingRoomsByLevel(level)
	if err != nil {
		return nil, err
	}

	// 如果找到等待中的房间，返回第一个
	if len(rooms) > 0 {
		// 按玩家数量排序，优先返回人数较多的房间
		sort.Slice(rooms, func(i, j int) bool {
			return rooms[i].PlayerCount > rooms[j].PlayerCount
		})

		// 返回第一个未满的房间
		for _, room := range rooms {
			if room.PlayerCount < room.MaxPlayers {
				return room, nil
			}
		}
	}

	// 创建新房间
	roomID := uuid.New().String()
	room := &model.ArenaRoom{
		RoomID:        roomID,
		Status:        model.RoomStatusWaiting,
		SubjectType:   "math",
		Level:         level,
		QuestionCount: 10,
		TimeLimit:     60,
		PlayerCount:   0,
		MaxPlayers:    100,
		ExpireAt:      time.Now().Add(24 * time.Hour),
		IsArchived:    false,
	}

	// 保存到数据库
	if err := s.arenaRepo.CreateRoom(room); err != nil {
		return nil, err
	}

	return room, nil
}

// GetRoomInfo 获取房间信息
func (s *arenaService) GetRoomInfo(roomID string) (*model.ArenaRoomResponse, error) {
	// 查找房间
	room, err := s.arenaRepo.FindRoomByID(roomID)
	if err != nil {
		return nil, err
	}
	if room == nil {
		return nil, errors.New("room not found")
	}

	// 查找房间内的玩家
	players, err := s.arenaRepo.FindPlayersByRoomID(roomID)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	playerResponses := make([]model.ArenaPlayerResponse, len(players))
	for i, player := range players {
		playerResponses[i] = model.ArenaPlayerResponse{
			UserID:       player.UserID,
			Nickname:     player.Nickname,
			Avatar:       player.Avatar,
			Status:       player.Status,
			Score:        player.Score,
			CorrectCount: player.CorrectCount,
			WrongCount:   player.WrongCount,
			Rank:         player.Rank,
			IsOnline:     player.IsOnline,
		}
	}

	// 计算倒计时
	var countdown int
	if room.Status == model.RoomStatusWaiting && room.PlayerCount >= 2 {
		// 如果房间状态为等待中且玩家数量大于等于2，倒计时为10秒
		countdown = 10
	}

	return &model.ArenaRoomResponse{
		RoomID:        room.RoomID,
		Status:        room.Status,
		SubjectType:   room.SubjectType,
		Level:         room.Level,
		QuestionCount: room.QuestionCount,
		TimeLimit:     room.TimeLimit,
		StartTime:     room.StartTime,
		PlayerCount:   room.PlayerCount,
		MaxPlayers:    room.MaxPlayers,
		Players:       playerResponses,
		Countdown:     countdown,
	}, nil
}

// StartRoom 开始房间
func (s *arenaService) StartRoom(roomID string) error {
	// 查找房间
	room, err := s.arenaRepo.FindRoomByID(roomID)
	if err != nil {
		return err
	}
	if room == nil {
		return errors.New("room not found")
	}

	// 更新房间状态
	room.Status = model.RoomStatusRunning
	room.StartTime = time.Now()
	return s.arenaRepo.UpdateRoom(room)
}

// FinishRoom 结束房间
func (s *arenaService) FinishRoom(roomID string) error {
	// 查找房间
	room, err := s.arenaRepo.FindRoomByID(roomID)
	if err != nil {
		return err
	}
	if room == nil {
		return errors.New("room not found")
	}

	// 更新房间状态
	room.Status = model.RoomStatusFinished
	room.EndTime = time.Now()
	return s.arenaRepo.UpdateRoom(room)
}

// AddPlayerToRoom 将玩家添加到房间
func (s *arenaService) AddPlayerToRoom(roomID string, userID uint, nickname string, avatar string) (*model.ArenaPlayer, string, error) {
	// 查找房间
	room, err := s.arenaRepo.FindRoomByID(roomID)
	if err != nil {
		return nil, "", err
	}
	if room == nil {
		return nil, "", errors.New("room not found")
	}

	// 检查房间状态
	if room.Status != model.RoomStatusWaiting {
		return nil, "", errors.New("room is not in waiting status")
	}

	// 生成会话ID
	sessionID := uuid.New().String()

	// 创建玩家
	player := &model.ArenaPlayer{
		RoomID:       roomID,
		UserID:       userID,
		SessionID:    sessionID,
		Nickname:     nickname,
		Avatar:       avatar,
		Status:       model.PlayerStatusWaiting,
		IsOnline:     true,
		LastActiveAt: time.Now(),
		LastPingTime: time.Now(),
	}

	// 保存到数据库
	if err := s.arenaRepo.CreatePlayer(player); err != nil {
		return nil, "", err
	}

	// 更新房间玩家数量
	room.PlayerCount++
	if err := s.arenaRepo.UpdateRoom(room); err != nil {
		return nil, "", err
	}

	return player, sessionID, nil
}

// GetPlayerInfo 获取玩家信息
func (s *arenaService) GetPlayerInfo(playerID uint) (*model.ArenaPlayerResponse, error) {
	// 查找玩家
	player, err := s.arenaRepo.FindPlayerByID(playerID)
	if err != nil {
		return nil, err
	}
	if player == nil {
		return nil, errors.New("player not found")
	}

	return &model.ArenaPlayerResponse{
		UserID:       player.UserID,
		Nickname:     player.Nickname,
		Avatar:       player.Avatar,
		Status:       player.Status,
		Score:        player.Score,
		CorrectCount: player.CorrectCount,
		WrongCount:   player.WrongCount,
		Rank:         player.Rank,
		IsOnline:     player.IsOnline,
	}, nil
}

// UpdatePlayerStatus 更新玩家状态
func (s *arenaService) UpdatePlayerStatus(playerID uint, status string) error {
	return s.arenaRepo.UpdatePlayerStatus(playerID, status)
}

// UpdatePlayerOnlineStatus 更新玩家在线状态
func (s *arenaService) UpdatePlayerOnlineStatus(playerID uint, isOnline bool) error {
	return s.arenaRepo.UpdatePlayerOnlineStatus(playerID, isOnline)
}

// GetRoomPlayers 获取房间内的所有玩家
func (s *arenaService) GetRoomPlayers(roomID string) ([]model.ArenaPlayerResponse, error) {
	// 查找房间内的玩家
	players, err := s.arenaRepo.FindPlayersByRoomID(roomID)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	playerResponses := make([]model.ArenaPlayerResponse, len(players))
	for i, player := range players {
		playerResponses[i] = model.ArenaPlayerResponse{
			UserID:       player.UserID,
			Nickname:     player.Nickname,
			Avatar:       player.Avatar,
			Status:       player.Status,
			Score:        player.Score,
			CorrectCount: player.CorrectCount,
			WrongCount:   player.WrongCount,
			Rank:         player.Rank,
			IsOnline:     player.IsOnline,
		}
	}

	return playerResponses, nil
}

// GenerateRoomQuestions 生成房间题目
func (s *arenaService) GenerateRoomQuestions(roomID string, level string, count int) ([]*model.ArenaQuestionResponse, error) {
	// 生成题目
	mathLevel := math.MathLevel(level)
	questions := s.mathGenerator.GenerateQuestions(mathLevel, count)

	// 转换为房间题目
	roomQuestions := make([]*model.ArenaQuestion, len(questions))
	for i, q := range questions {
		// 生成选项
		correctAnswer := q.Answer
		options := generateOptions(correctAnswer)

		roomQuestions[i] = &model.ArenaQuestion{
			RoomID:     roomID,
			QuestionID: q.ID,
			Question:   q.Question,
			Answer:     correctAnswer,
			OptionA:    options[0],
			OptionB:    options[1],
			OptionC:    options[2],
			OptionD:    options[3],
			Type:       string(q.Type),
			Level:      string(q.Level),
			Order:      i,
		}
	}

	// 保存到数据库
	if err := s.arenaRepo.CreateQuestions(roomQuestions); err != nil {
		return nil, err
	}

	// 转换为响应格式
	questionResponses := make([]*model.ArenaQuestionResponse, len(roomQuestions))
	for i, q := range roomQuestions {
		questionResponses[i] = &model.ArenaQuestionResponse{
			ID:       q.QuestionID,
			Question: q.Question,
			Options:  []int{q.OptionA, q.OptionB, q.OptionC, q.OptionD},
			Type:     q.Type,
			Level:    q.Level,
			Order:    q.Order,
		}
	}

	return questionResponses, nil
}

// GetRoomQuestions 获取房间题目
func (s *arenaService) GetRoomQuestions(roomID string) ([]*model.ArenaQuestionResponse, error) {
	// 查找房间题目
	questions, err := s.arenaRepo.FindQuestionsByRoomID(roomID)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	questionResponses := make([]*model.ArenaQuestionResponse, len(questions))
	for i, q := range questions {
		questionResponses[i] = &model.ArenaQuestionResponse{
			ID:       q.QuestionID,
			Question: q.Question,
			Options:  []int{q.OptionA, q.OptionB, q.OptionC, q.OptionD},
			Type:     q.Type,
			Level:    q.Level,
			Order:    q.Order,
		}
	}

	return questionResponses, nil
}

// SubmitAnswer 提交答案
func (s *arenaService) SubmitAnswer(roomID string, userID uint, questionID string, answer int, answerTime int) (bool, error) {
	// 查找题目
	questions, err := s.arenaRepo.FindQuestionsByRoomID(roomID)
	if err != nil {
		return false, err
	}

	// 查找对应题目
	var question *model.ArenaQuestion
	for _, q := range questions {
		if q.QuestionID == questionID {
			question = q
			break
		}
	}

	if question == nil {
		return false, errors.New("question not found")
	}

	// 检查答案是否正确
	var isCorrect bool
	switch answer {
	case 0:
		isCorrect = question.OptionA == question.Answer
	case 1:
		isCorrect = question.OptionB == question.Answer
	case 2:
		isCorrect = question.OptionC == question.Answer
	case 3:
		isCorrect = question.OptionD == question.Answer
	default:
		return false, errors.New("invalid answer")
	}

	// 保存答案
	arenaAnswer := &model.ArenaAnswer{
		RoomID:     roomID,
		UserID:     userID,
		QuestionID: questionID,
		UserAnswer: answer,
		IsCorrect:  isCorrect,
		AnswerTime: answerTime,
	}

	if err := s.arenaRepo.CreateAnswer(arenaAnswer); err != nil {
		return false, err
	}

	return isCorrect, nil
}

// CalculateResults 计算结果
func (s *arenaService) CalculateResults(roomID string) error {
	// 查找房间
	room, err := s.arenaRepo.FindRoomByID(roomID)
	if err != nil {
		return err
	}
	if room == nil {
		return errors.New("room not found")
	}

	// 查找房间内的玩家
	players, err := s.arenaRepo.FindPlayersByRoomID(roomID)
	if err != nil {
		return err
	}

	// 计算每个玩家的得分
	for _, player := range players {
		// 查找玩家的答案
		answers, err := s.arenaRepo.FindAnswersByUserAndRoom(player.UserID, roomID)
		if err != nil {
			return err
		}

		// 计算正确和错误的题目数量
		correctCount := 0
		for _, answer := range answers {
			if answer.IsCorrect {
				correctCount++
			}
		}
		wrongCount := len(answers) - correctCount

		// 计算得分（每道题10分）
		score := correctCount * 10

		// 更新玩家信息
		player.CorrectCount = correctCount
		player.WrongCount = wrongCount
		player.Score = score
		player.Status = model.RoomStatusFinished
		if err := s.arenaRepo.UpdatePlayer(player); err != nil {
			return err
		}

		// 创建排行榜记录
		ranking := &model.ArenaRanking{
			UserID:       player.UserID,
			Nickname:     player.Nickname,
			Avatar:       player.Avatar,
			SubjectType:  room.SubjectType,
			Level:        room.Level,
			Score:        score,
			CorrectCount: correctCount,
			WrongCount:   wrongCount,
			FinishTime:   player.FinishTime,
			RankDate:     time.Now(),
		}
		if err := s.arenaRepo.CreateRanking(ranking); err != nil {
			return err
		}
	}

	// 计算排名
	sort.Slice(players, func(i, j int) bool {
		// 按得分降序排序
		if players[i].Score != players[j].Score {
			return players[i].Score > players[j].Score
		}
		// 得分相同，按完成时间升序排序
		return players[i].FinishTime < players[j].FinishTime
	})

	// 更新排名
	for i, player := range players {
		player.Rank = i + 1
		if err := s.arenaRepo.UpdatePlayer(player); err != nil {
			return err
		}
	}

	return nil
}

// GetTodayTopRankings 获取今日排行榜
func (s *arenaService) GetTodayTopRankings(subjectType string, level string, limit int) ([]model.ArenaRankingResponse, error) {
	// 查找今日排行榜
	rankings, err := s.arenaRepo.FindTodayTopRankings(subjectType, level, limit)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	rankingResponses := make([]model.ArenaRankingResponse, len(rankings))
	for i, ranking := range rankings {
		rankingResponses[i] = model.ArenaRankingResponse{
			UserID:       ranking.UserID,
			Nickname:     ranking.Nickname,
			Avatar:       ranking.Avatar,
			Score:        ranking.Score,
			CorrectCount: ranking.CorrectCount,
			Rank:         ranking.Rank,
			RankDate:     ranking.RankDate.Format("2006-01-02"),
		}
	}

	return rankingResponses, nil
}

// GetUserRankingHistory 获取用户排行榜历史
func (s *arenaService) GetUserRankingHistory(userID uint, limit int) ([]model.ArenaRankingResponse, error) {
	// 查找用户排行榜历史
	rankings, err := s.arenaRepo.FindUserRankingHistory(userID, limit)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	rankingResponses := make([]model.ArenaRankingResponse, len(rankings))
	for i, ranking := range rankings {
		rankingResponses[i] = model.ArenaRankingResponse{
			UserID:       ranking.UserID,
			Nickname:     ranking.Nickname,
			Avatar:       ranking.Avatar,
			Score:        ranking.Score,
			CorrectCount: ranking.CorrectCount,
			Rank:         ranking.Rank,
			RankDate:     ranking.RankDate.Format("2006-01-02"),
		}
	}

	return rankingResponses, nil
}

// 生成选项
func generateOptions(correctAnswer int) []int {
	options := make([]int, 4)
	options[0] = correctAnswer

	// 生成其他三个选项
	for i := 1; i < 4; i++ {
		var option int
		for {
			// 生成一个与正确答案相差不超过5的随机数
			diff := rand.Intn(10) - 5
			if diff == 0 {
				diff = 1 // 避免生成与正确答案相同的选项
			}
			option = correctAnswer + diff

			// 确保选项是正数且不重复
			if option > 0 && !containsOption(options[:i], option) {
				break
			}
		}
		options[i] = option
	}

	// 随机打乱选项顺序
	rand.Shuffle(len(options), func(i, j int) {
		options[i], options[j] = options[j], options[i]
	})

	return options
}

// 检查选项是否已存在
func containsOption(options []int, option int) bool {
	for _, o := range options {
		if o == option {
			return true
		}
	}
	return false
}
