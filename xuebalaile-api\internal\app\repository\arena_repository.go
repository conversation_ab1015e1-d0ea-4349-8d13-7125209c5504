package repository

import (
	"errors"
	"time"

	"xuebalaile-api/internal/app/model"

	"gorm.io/gorm"
)

// ArenaRepository 打擂存储库接口
type ArenaRepository interface {
	// 房间相关
	CreateRoom(room *model.ArenaRoom) error
	FindRoomByID(roomID string) (*model.ArenaRoom, error)
	FindWaitingRoomsByLevel(level string) ([]*model.ArenaRoom, error)
	UpdateRoom(room *model.ArenaRoom) error
	ArchiveRoom(roomID string) error

	// 玩家相关
	CreatePlayer(player *model.ArenaPlayer) error
	FindPlayerByID(playerID uint) (*model.ArenaPlayer, error)
	FindPlayerBySessionID(sessionID string) (*model.ArenaPlayer, error)
	FindPlayersByRoomID(roomID string) ([]*model.ArenaPlayer, error)
	UpdatePlayer(player *model.ArenaPlayer) error
	UpdatePlayerStatus(playerID uint, status string) error
	UpdatePlayerOnlineStatus(playerID uint, isOnline bool) error

	// 题目相关
	CreateQuestions(questions []*model.ArenaQuestion) error
	FindQuestionsByRoomID(roomID string) ([]*model.ArenaQuestion, error)

	// 答案相关
	CreateAnswer(answer *model.ArenaAnswer) error
	FindAnswersByUserAndRoom(userID uint, roomID string) ([]*model.ArenaAnswer, error)

	// 排行榜相关
	CreateRanking(ranking *model.ArenaRanking) error
	FindTodayTopRankings(subjectType string, level string, limit int) ([]*model.ArenaRanking, error)
	FindUserRankingHistory(userID uint, limit int) ([]*model.ArenaRanking, error)
}

// arenaRepository 打擂存储库实现
type arenaRepository struct {
	db *gorm.DB
}

// NewArenaRepository 创建打擂存储库
func NewArenaRepository(db *gorm.DB) ArenaRepository {
	return &arenaRepository{db: db}
}

// CreateRoom 创建房间
func (r *arenaRepository) CreateRoom(room *model.ArenaRoom) error {
	return r.db.Create(room).Error
}

// FindRoomByID 根据ID查找房间
func (r *arenaRepository) FindRoomByID(roomID string) (*model.ArenaRoom, error) {
	var room model.ArenaRoom
	if err := r.db.Where("room_id = ?", roomID).First(&room).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &room, nil
}

// FindWaitingRoomsByLevel 查找指定难度的等待中房间
func (r *arenaRepository) FindWaitingRoomsByLevel(level string) ([]*model.ArenaRoom, error) {
	var rooms []*model.ArenaRoom
	if err := r.db.Where("level = ? AND status = ? AND is_archived = ?", level, model.RoomStatusWaiting, false).Find(&rooms).Error; err != nil {
		return nil, err
	}
	return rooms, nil
}

// UpdateRoom 更新房间
func (r *arenaRepository) UpdateRoom(room *model.ArenaRoom) error {
	return r.db.Save(room).Error
}

// ArchiveRoom 归档房间
func (r *arenaRepository) ArchiveRoom(roomID string) error {
	return r.db.Model(&model.ArenaRoom{}).Where("room_id = ?", roomID).Update("is_archived", true).Error
}

// CreatePlayer 创建玩家
func (r *arenaRepository) CreatePlayer(player *model.ArenaPlayer) error {
	return r.db.Create(player).Error
}

// FindPlayerByID 根据ID查找玩家
func (r *arenaRepository) FindPlayerByID(playerID uint) (*model.ArenaPlayer, error) {
	var player model.ArenaPlayer
	if err := r.db.Where("id = ?", playerID).First(&player).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &player, nil
}

// FindPlayerBySessionID 根据会话ID查找玩家
func (r *arenaRepository) FindPlayerBySessionID(sessionID string) (*model.ArenaPlayer, error) {
	var player model.ArenaPlayer
	if err := r.db.Where("session_id = ?", sessionID).First(&player).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &player, nil
}

// FindPlayersByRoomID 查找房间内的所有玩家
func (r *arenaRepository) FindPlayersByRoomID(roomID string) ([]*model.ArenaPlayer, error) {
	var players []*model.ArenaPlayer
	if err := r.db.Where("room_id = ?", roomID).Find(&players).Error; err != nil {
		return nil, err
	}
	return players, nil
}

// UpdatePlayer 更新玩家
func (r *arenaRepository) UpdatePlayer(player *model.ArenaPlayer) error {
	return r.db.Save(player).Error
}

// UpdatePlayerStatus 更新玩家状态
func (r *arenaRepository) UpdatePlayerStatus(playerID uint, status string) error {
	return r.db.Model(&model.ArenaPlayer{}).Where("id = ?", playerID).Update("status", status).Error
}

// UpdatePlayerOnlineStatus 更新玩家在线状态
func (r *arenaRepository) UpdatePlayerOnlineStatus(playerID uint, isOnline bool) error {
	updates := map[string]interface{}{
		"is_online": isOnline,
	}

	if !isOnline {
		updates["disconnect_at"] = time.Now()
	} else {
		updates["last_active_at"] = time.Now()
	}

	return r.db.Model(&model.ArenaPlayer{}).Where("id = ?", playerID).Updates(updates).Error
}

// CreateQuestions 创建题目
func (r *arenaRepository) CreateQuestions(questions []*model.ArenaQuestion) error {
	return r.db.Create(&questions).Error
}

// FindQuestionsByRoomID 查找房间内的所有题目
func (r *arenaRepository) FindQuestionsByRoomID(roomID string) ([]*model.ArenaQuestion, error) {
	var questions []*model.ArenaQuestion
	if err := r.db.Where("room_id = ?", roomID).Order("order asc").Find(&questions).Error; err != nil {
		return nil, err
	}
	return questions, nil
}

// CreateAnswer 创建答案
func (r *arenaRepository) CreateAnswer(answer *model.ArenaAnswer) error {
	return r.db.Create(answer).Error
}

// FindAnswersByUserAndRoom 查找用户在房间内的所有答案
func (r *arenaRepository) FindAnswersByUserAndRoom(userID uint, roomID string) ([]*model.ArenaAnswer, error) {
	var answers []*model.ArenaAnswer
	if err := r.db.Where("user_id = ? AND room_id = ?", userID, roomID).Find(&answers).Error; err != nil {
		return nil, err
	}
	return answers, nil
}

// CreateRanking 创建排行榜记录
func (r *arenaRepository) CreateRanking(ranking *model.ArenaRanking) error {
	return r.db.Create(ranking).Error
}

// FindTodayTopRankings 查找今日排行榜
func (r *arenaRepository) FindTodayTopRankings(subjectType string, level string, limit int) ([]*model.ArenaRanking, error) {
	today := time.Now().Format("2006-01-02")
	startOfDay, _ := time.Parse("2006-01-02", today)
	endOfDay := startOfDay.Add(24 * time.Hour)

	var rankings []*model.ArenaRanking
	if err := r.db.Where("subject_type = ? AND level = ? AND rank_date >= ? AND rank_date < ?",
		subjectType, level, startOfDay, endOfDay).
		Order("rank asc").
		Limit(limit).
		Find(&rankings).Error; err != nil {
		return nil, err
	}
	return rankings, nil
}

// FindUserRankingHistory 查找用户排行榜历史
func (r *arenaRepository) FindUserRankingHistory(userID uint, limit int) ([]*model.ArenaRanking, error) {
	var rankings []*model.ArenaRanking
	if err := r.db.Where("user_id = ?", userID).
		Order("created_at desc").
		Limit(limit).
		Find(&rankings).Error; err != nil {
		return nil, err
	}
	return rankings, nil
}
