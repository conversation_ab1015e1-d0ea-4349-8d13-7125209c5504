/**
 * 网络请求工具类
 */
import { ref } from 'vue';
import UNI_APP from '@/.env.js'

// 服务器地址
const baseURL = UNI_APP.BASE_URL;

// 全局请求状态
export const loading = ref(false);

// 请求配置接口
interface RequestOptions {
  url: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  data?: any;
  params?: Record<string, any>;
  headers?: Record<string, string>;
  showLoading?: boolean;
}

/**
 * 统一请求方法
 */
export const request = async <T = any>(options: RequestOptions): Promise<T> => {
  const { url, method = 'GET', data, params, headers = {}, showLoading = true } = options;
  
  // 显示加载状态
  if (showLoading) {
    loading.value = true;
    uni.showLoading({ title: '加载中...' });
  }
  
  // 获取token
  const token = uni.getStorageSync('token') || '';
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  // 构建完整URL
  let fullUrl = baseURL + url;
  
  // 处理GET请求参数
  if (params) {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    fullUrl += (fullUrl.includes('?') ? '&' : '?') + queryString;
  }
  
  return new Promise((resolve, reject) => {
    console.log("<<<<<< requesting:  ", fullUrl)
    console.log("requesting data:  ", data)
    uni.request({
      url: fullUrl,
      method,
      data,
      header: {
        'Content-Type': 'application/json',
        ...headers
      },
      success: (res: any) => {
        console.log(">>>>>> response res.statusCode: ", res.statusCode)
        // 请求成功但业务失败
        if (res.statusCode !== 200) {
          uni.showToast({
            title: `请求失败: ${res.statusCode}`,
            icon: 'none'
          });
          reject(res);
          return;
        }
        
        // 业务状态判断
        if (res.data.code !== 0) {
          uni.showToast({
            title: res.data.message || '请求失败',
            icon: 'none'
          });
          
          // 处理401未授权
          if (res.data.code === 401) {
            uni.removeStorageSync('token');
            uni.removeStorageSync('userInfo');
            
            // 跳转到登录页
            setTimeout(() => {
              uni.navigateTo({ url: '/pages/login/login' });
            }, 1500);
          }
          
          reject(res.data);
          return;
        }
        
        // 成功返回数据
        console.log("response res.data:  ", res.data)
        resolve(res.data.data);
      },
      fail: (err) => {
        console.log("requested err: ", err)
        
        uni.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        reject(err);
      },
      complete: () => {
        if (showLoading) {
          loading.value = false;
          uni.hideLoading();
        }
      }
    });
  });
};

// 封装常用请求方法
export const get = <T = any>(url: string, params?: Record<string, any>, options?: Partial<RequestOptions>) => {
  return request<T>({ url, method: 'GET', params, ...options });
};

export const post = <T = any>(url: string, data?: any, options?: Partial<RequestOptions>) => {
  return request<T>({ url, method: 'POST', data, ...options });
};

export const put = <T = any>(url: string, data?: any, options?: Partial<RequestOptions>) => {
  return request<T>({ url, method: 'PUT', data, ...options });
};

export const del = <T = any>(url: string, data?: any, options?: Partial<RequestOptions>) => {
  return request<T>({ url, method: 'DELETE', data, ...options });
};
