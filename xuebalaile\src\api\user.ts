/**
 * 用户相关API
 */
import { get, post } from '../utils/request';
import type { UserInfo } from '@/types/user'; // 明确使用 import type

// 登录参数接口
export interface LoginParams {
  code: string;
  platform: 'wechat' | 'xiaohongshu';
}

/**
 * 小程序授权登录
 */
export const login = (params: LoginParams) => {
  return post<{ token: string; expiresAt: string; notBefore: string; userInfo: UserInfo }>('/user/login', params);
};

/**
 * 获取用户信息
 */
export const getUserInfo = () => {
  return get<UserInfo>('/user/info');
};
