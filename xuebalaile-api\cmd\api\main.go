package main

import (
	"fmt"
	"log"

	"xuebalaile-api/internal/app/handler"
	"xuebalaile-api/internal/app/middleware"
	"xuebalaile-api/internal/app/model"
	"xuebalaile-api/internal/app/repository"
	"xuebalaile-api/internal/app/service"
	"xuebalaile-api/internal/pkg/auth"
	"xuebalaile-api/internal/pkg/config"
	"xuebalaile-api/internal/pkg/db"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	cfg, err := config.LoadConfig("./config/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 初始化数据库
	database, err := db.NewDB(cfg)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 自动迁移数据库表
	if err := database.AutoMigrate(
		&model.User{},
		&model.MathQuestion{},
		&model.MathResult{},
		&model.WrongQuestion{},
		&model.ArenaRoom{},
		&model.ArenaPlayer{},
		&model.ArenaQuestion{},
		&model.ArenaAnswer{},
		&model.ArenaRanking{},
	); err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// 初始化JWT服务
	jwtService := auth.NewJWTService(cfg)

	// 初始化存储库
	userRepo := repository.NewUserRepository(database)
	mathRepo := repository.NewMathRepository(database)
	arenaRepo := repository.NewArenaRepository(database)

	// 初始化服务
	userService := service.NewUserService(userRepo, jwtService, cfg)
	mathService := service.NewMathService(mathRepo)
	arenaService := service.NewArenaService(arenaRepo)

	// 初始化处理器
	userHandler := handler.NewUserHandler(userService)
	mathHandler := handler.NewMathHandler(mathService)
	arenaHandler := handler.NewArenaHandler(arenaService, userService, jwtService)

	// 创建Gin引擎
	router := gin.Default()

	// 添加中间件
	router.Use(middleware.CORSMiddleware())

	// 创建API路由组
	api := router.Group("/api")

	// 注册路由
	userHandler.RegisterRoutes(api.Group("/user"), middleware.AuthMiddleware(jwtService))
	mathHandler.RegisterRoutes(api.Group("/math"), middleware.AuthMiddleware(jwtService))
	arenaHandler.RegisterRoutes(api.Group("/arena"), middleware.AuthMiddleware(jwtService))

	// 启动服务器
	addr := fmt.Sprintf(":%d", cfg.Server.Port)
	log.Printf("Server is running on %s", addr)
	if err := router.Run(addr); err != nil {
		log.Fatalf("Failed to start server: %v", err)
	}
}
