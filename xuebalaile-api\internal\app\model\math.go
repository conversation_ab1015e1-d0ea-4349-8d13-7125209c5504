package model

import (
	"time"

	"xuebalaile-api/internal/pkg/math"

	"gorm.io/gorm"
)

// MathQuestion 口算题目模型
type MathQuestion struct {
	gorm.Model
	QuestionID string `gorm:"size:50;uniqueIndex"`
	Question   string `gorm:"size:50;not null"`
	Answer     int    `gorm:"not null"`
	Type       string `gorm:"size:20;not null"` // addition, subtraction
	Level      string `gorm:"size:50;not null"`
}

// MathResult 口算结果模型
type MathResult struct {
	gorm.Model
	UserID         uint    `gorm:"not null;index"`
	Level          string  `gorm:"size:50;not null"`
	TotalQuestions int     `gorm:"not null"`
	CorrectCount   int     `gorm:"not null"`
	WrongCount     int     `gorm:"not null"`
	TimeUsed       int     `gorm:"not null"` // 秒数
	Score          float64 `gorm:"not null"`
}

// WrongQuestion 错题模型
type WrongQuestion struct {
	gorm.Model
	UserID      uint   `gorm:"not null;index"`
	QuestionID  string `gorm:"size:50;not null"`
	Question    string `gorm:"size:50;not null"`
	Answer      int    `gorm:"not null"`
	UserAnswer  int    `gorm:"not null"`
	Type        string `gorm:"size:20;not null"` // addition, subtraction
	Level       string `gorm:"size:50;not null"`
	IsCorrected bool   `gorm:"default:false"`
}

// MathQuestionResponse 口算题目响应
type MathQuestionResponse struct {
	ID       string         `json:"id"`
	Question string         `json:"question"`
	Answer   int            `json:"answer,omitempty"`
	Type     math.MathType  `json:"type"`
	Level    math.MathLevel `json:"level"`
}

// MathResultResponse 口算结果响应
type MathResultResponse struct {
	ID             uint                    `json:"id"`
	Level          math.MathLevel          `json:"level"`
	TotalQuestions int                     `json:"totalQuestions"`
	CorrectCount   int                     `json:"correctCount"`
	WrongCount     int                     `json:"wrongCount"`
	TimeUsed       int                     `json:"timeUsed"`
	Score          float64                 `json:"score"`
	WrongQuestions []WrongQuestionResponse `json:"wrongQuestions"`
	CreatedAt      time.Time               `json:"createdAt"`
}

// WrongQuestionResponse 错题响应
type WrongQuestionResponse struct {
	Question   MathQuestionResponse `json:"question"`
	UserAnswer int                  `json:"userAnswer"`
}

// CompetitionLevelResponse 练习级别响应
type CompetitionLevelResponse struct {
	Name        string         `json:"name"`
	Value       math.MathLevel `json:"value"`
	Description string         `json:"description"`
}

// ToResponse 转换为响应
func (q *MathQuestion) ToResponse(hideAnswer bool) *MathQuestionResponse {
	resp := &MathQuestionResponse{
		ID:       q.QuestionID,
		Question: q.Question,
		Type:     math.MathType(q.Type),
		Level:    math.MathLevel(q.Level),
	}

	if !hideAnswer {
		resp.Answer = q.Answer
	}

	return resp
}

// ToResponse 转换为响应
func (r *MathResult) ToResponse(wrongQuestions []WrongQuestionResponse) *MathResultResponse {
	return &MathResultResponse{
		ID:             r.ID,
		Level:          math.MathLevel(r.Level),
		TotalQuestions: r.TotalQuestions,
		CorrectCount:   r.CorrectCount,
		WrongCount:     r.WrongCount,
		TimeUsed:       r.TimeUsed,
		Score:          r.Score,
		WrongQuestions: wrongQuestions,
		CreatedAt:      r.CreatedAt,
	}
}

// ToResponse 转换为响应
func (w *WrongQuestion) ToResponse() *WrongQuestionResponse {
	return &WrongQuestionResponse{
		Question: MathQuestionResponse{
			ID:       w.QuestionID,
			Question: w.Question,
			Answer:   w.Answer,
			Type:     math.MathType(w.Type),
			Level:    math.MathLevel(w.Level),
		},
		UserAnswer: w.UserAnswer,
	}
}
