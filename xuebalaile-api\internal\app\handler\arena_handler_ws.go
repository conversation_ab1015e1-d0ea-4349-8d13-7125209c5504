package handler

import (
	"encoding/json"
	"log"
	"os"
	"time"
	"xuebalaile-api/internal/app/model"

	"github.com/mitchellh/mapstructure"
)

// handleMessages 处理WebSocket消息
func (h *ArenaHandler) handleMessages(player *PlayerConnection) {
	defer func() {
		// 关闭连接时清理资源
		log.Printf("[WebSocket] 用户断开连接: userID=%d, nickname=%s", player.UserID, player.Nickname)
		h.handlePlayerDisconnect(player)
		player.Conn.Close()
	}()

	log.Printf("[WebSocket] 新连接建立: userID=%d, nickname=%s", player.UserID, player.Nickname)

	for {
		// 读取消息
		_, message, err := player.Conn.ReadMessage()
		if err != nil {
			log.Printf("[WebSocket] 读取消息错误: userID=%d, error=%v", player.UserID, err)
			break
		}

		// 记录原始消息
		log.Printf("[WebSocket] 收到消息: userID=%d, message=%s", player.UserID, string(message))

		// 解析消息
		var msg WSMessage
		if err := json.Unmarshal(message, &msg); err != nil {
			log.Printf("[WebSocket] 解析消息错误: userID=%d, error=%v", player.UserID, err)
			continue
		}

		// 记录解析后的消息
		msgDataBytes, _ := json.Marshal(msg.Data)
		log.Printf("[WebSocket] 处理消息: userID=%d, type=%s, data=%s", player.UserID, msg.Type, string(msgDataBytes))

		// 处理不同类型的消息
		switch msg.Type {
		case MsgTypeJoinRoom:
			h.handleJoinRoom(player, msg.Data)
		case MsgTypeLeaveRoom:
			h.handleLeaveRoom(player)
		case MsgTypeSubmitAnswer:
			h.handleSubmitAnswer(player, msg.Data)
		case MsgTypePing:
			h.handlePing(player)
		case MsgTypeReconnect:
			h.handleReconnect(player, msg.Data)
		default:
			log.Printf("[WebSocket] 未知消息类型: userID=%d, type=%s", player.UserID, msg.Type)
		}
	}
}

// handleJoinRoom 处理加入房间请求
func (h *ArenaHandler) handleJoinRoom(player *PlayerConnection, data interface{}) {
	var req struct {
		Level string `json:"level"`
	}

	if err := mapstructure.Decode(data, &req); err != nil {
		log.Printf("[WebSocket] 解析加入房间请求失败: userID=%d, error=%v", player.UserID, err)
		h.sendError(player, "Invalid request format")
		return
	}

	log.Printf("[WebSocket] 处理加入房间请求: userID=%d, level=%s", player.UserID, req.Level)

	// 查找或创建房间
	room, err := h.arenaService.FindOrCreateRoom(req.Level)
	if err != nil {
		log.Printf("[WebSocket] 查找或创建房间失败: userID=%d, level=%s, error=%v", player.UserID, req.Level, err)
		h.sendError(player, "Failed to find or create room")
		return
	}

	log.Printf("[WebSocket] 找到房间: userID=%d, roomID=%s, status=%s, playerCount=%d",
		player.UserID, room.RoomID, room.Status, room.PlayerCount)

	// 将玩家添加到房间
	_, sessionID, err := h.arenaService.AddPlayerToRoom(room.RoomID, player.UserID, player.Nickname, player.Avatar)
	if err != nil {
		log.Printf("[WebSocket] 添加玩家到房间失败: userID=%d, roomID=%s, error=%v", player.UserID, room.RoomID, err)
		h.sendError(player, "Failed to join room")
		return
	}

	log.Printf("[WebSocket] 添加玩家到房间成功: userID=%d, roomID=%s, sessionID=%s", player.UserID, room.RoomID, sessionID)

	// 更新玩家连接信息
	player.RoomID = room.RoomID
	player.SessionID = sessionID

	// 将玩家添加到房间管理器
	h.roomManager.mu.Lock()
	log.Printf("[WebSocket] room.RoomID:%v, roomManager.mu.Lock", room.RoomID)
	if _, exists := h.roomManager.rooms[room.RoomID]; !exists {
		log.Printf("[WebSocket] not exists room.RoomID:%v int roomManager", room.RoomID)
		// 创建房间信息
		log.Printf("[WebSocket] 在内存中创建房间: roomID=%s, level=%s", room.RoomID, room.Level)
		h.roomManager.rooms[room.RoomID] = &RoomInfo{
			RoomID:    room.RoomID,
			Status:    room.Status,
			Level:     room.Level,
			Players:   make(map[uint]*PlayerInfo),
			TimeLimit: room.TimeLimit,
		}
	}

	// 创建玩家信息
	playerInfo := &PlayerInfo{
		UserID:       player.UserID,
		Nickname:     player.Nickname,
		Avatar:       player.Avatar,
		Conn:         player.Conn,
		SessionID:    sessionID,
		Status:       model.PlayerStatusWaiting,
		CurrentIndex: 0,
		IsOnline:     true,
		LastActiveAt: time.Now(),
	}

	// 添加玩家到房间
	h.roomManager.rooms[room.RoomID].Players[player.UserID] = playerInfo
	h.roomManager.players[player.UserID] = playerInfo
	h.roomManager.sessions[sessionID] = player.UserID

	// 获取当前房间人数
	playerCount := len(h.roomManager.rooms[room.RoomID].Players)
	h.roomManager.mu.Unlock()

	log.Printf("[WebSocket] 玩家加入房间内存管理: userID=%d, roomID=%s, 当前房间人数=%d", player.UserID, room.RoomID, playerCount)

	// 发送加入房间成功消息
	h.sendMessage(player, "join_room_result", map[string]interface{}{
		"success":   true,
		"roomId":    room.RoomID,
		"sessionId": sessionID,
	})

	// 广播房间状态更新
	h.broadcastRoomStatus(room.RoomID)

	// 如果房间人数达到2人，开始倒计时
	roomInfo := h.roomManager.rooms[room.RoomID]
	roomInfo.mu.Lock()
	log.Printf("[WebSocket] room.RoomID:%v, roomInfo.mu.Lock", room.RoomID)
	if len(roomInfo.Players) >= 2 && roomInfo.StartTimer == nil {
		log.Printf("[WebSocket] 房间人数达到2人，开始10秒倒计时: roomID=%s, playerCount=%d", room.RoomID, len(roomInfo.Players))
		roomInfo.StartTimer = time.AfterFunc(10*time.Second, func() {
			h.startGame(room.RoomID)
		})
	}
	roomInfo.mu.Unlock()

	// 开始倒计时广播
	if roomInfo.StartTimer != nil {
		go h.broadcastCountdown(room.RoomID, 10)
	}
}

// handleLeaveRoom 处理离开房间请求
func (h *ArenaHandler) handleLeaveRoom(player *PlayerConnection) {
	if player.RoomID == "" {
		log.Printf("[WebSocket] 离开房间失败: userID=%d, 玩家不在任何房间中", player.UserID)
		return
	}

	log.Printf("[WebSocket] 处理离开房间请求: userID=%d, roomID=%s", player.UserID, player.RoomID)

	h.roomManager.mu.Lock()
	defer h.roomManager.mu.Unlock()

	roomInfo, exists := h.roomManager.rooms[player.RoomID]
	if !exists {
		log.Printf("[WebSocket] 离开房间失败: userID=%d, roomID=%s, 房间不存在", player.UserID, player.RoomID)
		return
	}

	// 从房间中移除玩家
	roomInfo.mu.Lock()
	delete(roomInfo.Players, player.UserID)
	playerCount := len(roomInfo.Players)
	roomInfo.mu.Unlock()

	log.Printf("[WebSocket] 从房间中移除玩家: userID=%d, roomID=%s, 剩余玩家数=%d", player.UserID, player.RoomID, playerCount)

	// 从管理器中移除玩家
	delete(h.roomManager.players, player.UserID)
	delete(h.roomManager.sessions, player.SessionID)

	log.Printf("[WebSocket] 从管理器中移除玩家: userID=%d, sessionID=%s", player.UserID, player.SessionID)

	// 更新玩家在线状态
	h.arenaService.UpdatePlayerOnlineStatus(player.UserID, false)

	// 清除玩家房间信息
	roomID := player.RoomID
	player.RoomID = ""
	player.SessionID = ""

	log.Printf("[WebSocket] 玩家成功离开房间: userID=%d, roomID=%s", player.UserID, roomID)

	// 检查房间是否还有玩家，如果没有玩家且房间状态为running，则结束房间
	if playerCount == 0 && roomInfo.Status == model.RoomStatusRunning {
		log.Printf("[WebSocket] 房间中没有玩家，自动结束游戏: roomID=%s", roomID)
		// 解锁互斥锁，避免死锁（因为finishGame函数内部也会获取锁）
		h.roomManager.mu.Unlock()
		h.finishGame(roomID)
		// 重新获取锁，保持defer h.roomManager.mu.Unlock()的正确性
		h.roomManager.mu.Lock()
	} else {
		// 广播房间状态更新
		h.broadcastRoomStatus(roomID)
	}
}

// handlePlayerDisconnect 处理玩家断开连接
func (h *ArenaHandler) handlePlayerDisconnect(player *PlayerConnection) {
	if player.RoomID == "" {
		log.Printf("[WebSocket] 断开连接处理: userID=%d, 玩家不在任何房间中", player.UserID)
		return
	}

	log.Printf("[WebSocket] 处理玩家断开连接: userID=%d, roomID=%s", player.UserID, player.RoomID)

	h.roomManager.mu.Lock()
	defer h.roomManager.mu.Unlock()

	roomInfo, exists := h.roomManager.rooms[player.RoomID]
	if !exists {
		log.Printf("[WebSocket] 断开连接处理失败: userID=%d, roomID=%s, 房间不存在", player.UserID, player.RoomID)
		return
	}

	// 更新玩家在线状态
	playerInfo, exists := roomInfo.Players[player.UserID]
	if exists {
		playerInfo.IsOnline = false
		playerInfo.mu.Lock()
		playerInfo.Conn = nil
		playerInfo.mu.Unlock()
		log.Printf("[WebSocket] 更新玩家在线状态: userID=%d, roomID=%s, isOnline=false", player.UserID, player.RoomID)
	} else {
		log.Printf("[WebSocket] 更新玩家在线状态失败: userID=%d, roomID=%s, 玩家不在房间中", player.UserID, player.RoomID)
	}

	// 更新数据库中的玩家状态
	h.arenaService.UpdatePlayerOnlineStatus(player.UserID, false)

	// 检查房间中是否还有在线玩家
	onlineCount := 0
	roomInfo.mu.RLock()
	for _, p := range roomInfo.Players {
		p.mu.Lock()
		if p.IsOnline {
			onlineCount++
		}
		p.mu.Unlock()
	}
	roomInfo.mu.RUnlock()

	log.Printf("[WebSocket] 房间在线玩家检查: roomID=%s, 在线玩家数=%d", player.RoomID, onlineCount)

	// 如果没有在线玩家且房间状态为running，则结束房间
	if onlineCount == 0 && roomInfo.Status == model.RoomStatusRunning {
		log.Printf("[WebSocket] 房间中没有在线玩家，自动结束游戏: roomID=%s", player.RoomID)
		// 解锁互斥锁，避免死锁（因为finishGame函数内部也会获取锁）
		h.roomManager.mu.Unlock()
		h.finishGame(player.RoomID)
		// 重新获取锁，保持defer h.roomManager.mu.Unlock()的正确性
		h.roomManager.mu.Lock()
	} else {
		// 广播房间状态更新
		h.broadcastRoomStatus(player.RoomID)
	}

	log.Printf("[WebSocket] 玩家断开连接处理完成: userID=%d, roomID=%s", player.UserID, player.RoomID)
}

// handleSubmitAnswer 处理提交答案请求
func (h *ArenaHandler) handleSubmitAnswer(player *PlayerConnection, data interface{}) {
	var req struct {
		QuestionID string `json:"questionId"`
		Answer     int    `json:"answer"`
		AnswerTime int    `json:"answerTime"`
	}

	if err := mapstructure.Decode(data, &req); err != nil {
		log.Printf("[WebSocket] 解析提交答案请求失败: userID=%d, error=%v", player.UserID, err)
		h.sendError(player, "Invalid request format")
		return
	}

	log.Printf("[WebSocket] 处理提交答案请求: userID=%d, roomID=%s, questionID=%s, answer=%d, answerTime=%d",
		player.UserID, player.RoomID, req.QuestionID, req.Answer, req.AnswerTime)

	// 提交答案
	isCorrect, err := h.arenaService.SubmitAnswer(player.RoomID, player.UserID, req.QuestionID, req.Answer, req.AnswerTime)
	if err != nil {
		log.Printf("[WebSocket] 提交答案失败: userID=%d, roomID=%s, questionID=%s, error=%v",
			player.UserID, player.RoomID, req.QuestionID, err)
		h.sendError(player, "Failed to submit answer")
		return
	}

	log.Printf("[WebSocket] 提交答案成功: userID=%d, roomID=%s, questionID=%s, isCorrect=%v",
		player.UserID, player.RoomID, req.QuestionID, isCorrect)

	// 更新玩家信息
	h.roomManager.mu.RLock()
	roomInfo, exists := h.roomManager.rooms[player.RoomID]
	if !exists {
		log.Printf("[WebSocket] 房间不存在: userID=%d, roomID=%s", player.UserID, player.RoomID)
		h.roomManager.mu.RUnlock()
		return
	}

	playerInfo, exists := roomInfo.Players[player.UserID]
	if !exists {
		log.Printf("[WebSocket] 玩家不在房间中: userID=%d, roomID=%s", player.UserID, player.RoomID)
		h.roomManager.mu.RUnlock()
		return
	}

	playerInfo.mu.Lock()
	playerInfo.CurrentIndex++
	if isCorrect {
		playerInfo.CorrectCount++
		playerInfo.Score += 10 // 每题10分
	} else {
		playerInfo.WrongCount++
	}
	currentIndex := playerInfo.CurrentIndex
	score := playerInfo.Score
	correctCount := playerInfo.CorrectCount
	wrongCount := playerInfo.WrongCount
	playerInfo.mu.Unlock()
	h.roomManager.mu.RUnlock()

	log.Printf("[WebSocket] 更新玩家信息: userID=%d, roomID=%s, currentIndex=%d, score=%d, correctCount=%d, wrongCount=%d",
		player.UserID, player.RoomID, currentIndex, score, correctCount, wrongCount)

	// 发送答案结果
	h.sendMessage(player, "submit_answer_result", map[string]interface{}{
		"success":   true,
		"isCorrect": isCorrect,
		"score":     score,
	})

	// 广播玩家进度
	h.broadcastPlayerProgress(player.RoomID)

	// 检查是否所有题目都已回答
	roomInfo.mu.RLock()
	questionCount := len(roomInfo.Questions)
	roomInfo.mu.RUnlock()

	log.Printf("[WebSocket] 检查答题进度: userID=%d, roomID=%s, currentIndex=%d, questionCount=%d",
		player.UserID, player.RoomID, currentIndex, questionCount)

	if currentIndex >= questionCount {
		// 玩家完成所有题目
		log.Printf("[WebSocket] 玩家完成所有题目: userID=%d, roomID=%s", player.UserID, player.RoomID)
		h.arenaService.UpdatePlayerStatus(player.UserID, model.PlayerStatusFinished)

		// 检查是否所有玩家都已完成
		allFinished := true
		onlineCount := 0
		finishedCount := 0

		roomInfo.mu.RLock()
		for uid, p := range roomInfo.Players {
			p.mu.Lock()
			if p.IsOnline {
				onlineCount++
				if p.Status == model.PlayerStatusFinished {
					finishedCount++
				} else {
					allFinished = false
				}
			}
			p.mu.Unlock()

			log.Printf("[WebSocket] 玩家状态: roomID=%s, userID=%d, isOnline=%v, status=%s",
				player.RoomID, uid, p.IsOnline, p.Status)
		}
		roomInfo.mu.RUnlock()

		log.Printf("[WebSocket] 房间状态检查: roomID=%s, 在线玩家=%d, 已完成玩家=%d, 全部完成=%v",
			player.RoomID, onlineCount, finishedCount, allFinished)

		if allFinished && onlineCount > 0 {
			// 结束游戏
			log.Printf("[WebSocket] 所有玩家都已完成，结束游戏: roomID=%s", player.RoomID)
			h.finishGame(player.RoomID)
		}
	}
}

// handlePing 处理心跳请求
func (h *ArenaHandler) handlePing(player *PlayerConnection) {
	player.LastPing = time.Now()

	// 更新玩家活跃时间
	h.roomManager.mu.RLock()
	playerInfo, exists := h.roomManager.players[player.UserID]
	if exists {
		playerInfo.LastActiveAt = time.Now()
	}
	h.roomManager.mu.RUnlock()

	// 发送pong响应
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	h.sendMessage(player, "pong", map[string]interface{}{
		"timestamp": timestamp,
	})

	// 只在调试模式下记录心跳日志，避免日志过多
	if os.Getenv("DEBUG") == "true" {
		log.Printf("[WebSocket] 处理心跳请求: userID=%d, timestamp=%d", player.UserID, timestamp)
	}
}

// handleReconnect 处理重连请求
func (h *ArenaHandler) handleReconnect(player *PlayerConnection, data interface{}) {
	var req struct {
		SessionID string `json:"sessionId"`
	}

	if err := mapstructure.Decode(data, &req); err != nil {
		log.Printf("[WebSocket] 解析重连请求失败: userID=%d, error=%v", player.UserID, err)
		h.sendError(player, "Invalid request format")
		return
	}

	log.Printf("[WebSocket] 处理重连请求: userID=%d, sessionID=%s", player.UserID, req.SessionID)

	// 验证会话ID
	h.roomManager.mu.RLock()
	userID, exists := h.roomManager.sessions[req.SessionID]
	if !exists {
		h.roomManager.mu.RUnlock()
		log.Printf("[WebSocket] 重连失败: 会话ID不存在或已过期, userID=%d, sessionID=%s", player.UserID, req.SessionID)
		h.sendMessage(player, "reconnect_result", map[string]interface{}{
			"success": false,
			"message": "Session expired or not found",
		})
		return
	}

	log.Printf("[WebSocket] 会话ID验证成功: sessionID=%s, userID=%d", req.SessionID, userID)

	// 查找玩家信息
	playerInfo, exists := h.roomManager.players[userID]
	if !exists {
		h.roomManager.mu.RUnlock()
		log.Printf("[WebSocket] 重连失败: 玩家信息不存在, userID=%d, sessionID=%s", userID, req.SessionID)
		h.sendMessage(player, "reconnect_result", map[string]interface{}{
			"success": false,
			"message": "Player not found",
		})
		return
	}

	log.Printf("[WebSocket] 找到玩家信息: userID=%d, sessionID=%s", userID, req.SessionID)

	// 获取房间信息
	roomID := playerInfo.SessionID
	roomInfo, exists := h.roomManager.rooms[roomID]
	if !exists {
		h.roomManager.mu.RUnlock()
		log.Printf("[WebSocket] 重连失败: 房间不存在, userID=%d, roomID=%s", userID, roomID)
		h.sendMessage(player, "reconnect_result", map[string]interface{}{
			"success": false,
			"message": "Room not found",
		})
		return
	}
	h.roomManager.mu.RUnlock()

	log.Printf("[WebSocket] 找到房间信息: userID=%d, roomID=%s", userID, roomID)

	// 更新玩家连接
	playerInfo.mu.Lock()
	playerInfo.Conn = player.Conn
	playerInfo.IsOnline = true
	playerInfo.LastActiveAt = time.Now()
	playerInfo.mu.Unlock()

	log.Printf("[WebSocket] 更新玩家连接: userID=%d, roomID=%s", userID, roomID)

	// 更新玩家连接信息
	player.RoomID = roomID
	player.SessionID = req.SessionID

	// 更新数据库中的玩家状态
	h.arenaService.UpdatePlayerOnlineStatus(userID, true)

	// 根据房间状态返回不同的数据
	roomInfo.mu.RLock()
	roomStatus := roomInfo.Status
	roomInfo.mu.RUnlock()

	log.Printf("[WebSocket] 房间当前状态: roomID=%s, status=%s", roomID, roomStatus)

	// 发送重连成功消息
	h.sendMessage(player, "reconnect_result", map[string]interface{}{
		"success":    true,
		"roomId":     roomID,
		"roomStatus": roomStatus,
	})

	// 根据房间状态发送相应数据
	if roomStatus == model.RoomStatusWaiting {
		log.Printf("[WebSocket] 发送等待房间状态: userID=%d, roomID=%s", userID, roomID)
		// 发送房间状态
		h.sendRoomStatus(player)
	} else if roomStatus == model.RoomStatusRunning {
		log.Printf("[WebSocket] 发送游戏状态: userID=%d, roomID=%s", userID, roomID)
		// 发送游戏状态
		h.sendGameStatus(player)
	} else if roomStatus == model.RoomStatusFinished {
		log.Printf("[WebSocket] 发送游戏结果: userID=%d, roomID=%s", userID, roomID)
		// 发送游戏结果
		h.sendGameResult(player)
	}

	log.Printf("[WebSocket] 重连成功，广播房间状态: userID=%d, roomID=%s", userID, roomID)

	// 广播玩家重连消息
	h.broadcastRoomStatus(roomID)
}
