<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { initUserState, isLoggedIn } from './store/user';

// 应用启动时
onLaunch(() => {
  console.log("@@@@ App Launch");

  // 初始化用户状态
  initUserState();

  // 检查更新（微信小程序）
  // #ifdef MP-WEIXIN
  const updateManager = uni.getUpdateManager();

  updateManager.onCheckForUpdate((res) => {
    console.log('检查更新:', res.hasUpdate);
  });

  updateManager.onUpdateReady(() => {
    uni.showModal({
      title: '更新提示',
      content: '新版本已经准备好，是否重启应用？',
      success: (res) => {
        if (res.confirm) {
          updateManager.applyUpdate();
        }
      }
    });
  });

  updateManager.onUpdateFailed(() => {
    uni.showToast({
      title: '更新失败，请检查网络',
      icon: 'none'
    });
  });
  // #endif
    
    console.log("App Launch, reLaunch, isLoggedIn:", isLoggedIn);
      uni.reLaunch({
        url: isLoggedIn.value?"/pages/index/index":"/pages/login/login"
      })
});

// 应用显示时
onShow(() => {
  console.log("App Show");
});

// 应用隐藏时
onHide(() => {
  console.log("App Hide");
});
</script>

<style>
/* 全局样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'miui', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  font-size: 28rpx;
  line-height: 1.5;
  color: #333333;
  background-color: #f8f8f8;
  box-sizing: border-box;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 去除按钮默认边框 */
button {
  padding: 0;
  margin: 0;
  border-radius: 0;
  background-color: transparent;
  line-height: normal;
}

button::after {
  border: none;
}

/* 去除滚动条 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  background-color: transparent;
}
</style>
