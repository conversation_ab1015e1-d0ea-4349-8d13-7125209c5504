package handler

import (
	"encoding/json"
	"log"
	"time"
	"xuebalaile-api/internal/app/model"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// sendMessage 发送WebSocket消息
func (h *ArenaHandler) sendMessage(player *PlayerConnection, msgType string, data interface{}) {
	msg := WSMessage{
		Type:    msgType,
		Data:    data,
		Success: true,
	}

	// 记录发送的消息
	msgBytes, _ := json.Marshal(msg)
	log.Printf("[WebSocket] 发送消息: userID=%d, type=%s, message=%s", player.UserID, msgType, string(msgBytes))

	player.mu.Lock()
	defer player.mu.Unlock()

	if err := player.Conn.WriteJSON(msg); err != nil {
		log.Printf("[WebSocket] 发送消息错误: userID=%d, type=%s, error=%v", player.UserID, msgType, err)
	} else {
		log.Printf("[WebSocket] 消息发送成功: userID=%d, type=%s", player.UserID, msgType)
	}
}

// sendError 发送错误消息
func (h *ArenaHandler) sendError(player *PlayerConnection, message string) {
	msg := WSMessage{
		Type:    "error",
		Success: false,
		Message: message,
	}

	log.Printf("[WebSocket] 发送错误消息: userID=%d, message=%s", player.UserID, message)

	player.mu.Lock()
	defer player.mu.Unlock()

	if err := player.Conn.WriteJSON(msg); err != nil {
		log.Printf("[WebSocket] 发送错误消息失败: userID=%d, error=%v", player.UserID, err)
	} else {
		log.Printf("[WebSocket] 错误消息发送成功: userID=%d", player.UserID)
	}
}

// broadcastMessage 广播消息给房间内的所有玩家
func (h *ArenaHandler) broadcastMessage(roomID string, msgType string, data interface{}) {
	h.roomManager.mu.RLock()
	roomInfo, exists := h.roomManager.rooms[roomID]
	if !exists {
		log.Printf("[WebSocket] 广播消息失败: roomID=%s, 房间不存在", roomID)
		h.roomManager.mu.RUnlock()
		return
	}

	msg := WSMessage{
		Type:    msgType,
		Data:    data,
		Success: true,
	}

	// 记录广播的消息
	msgBytes, _ := json.Marshal(msg)
	log.Printf("[WebSocket] 广播消息: roomID=%s, type=%s, message=%s", roomID, msgType, string(msgBytes))

	jsonMsg, err := json.Marshal(msg)
	if err != nil {
		log.Printf("[WebSocket] 广播消息序列化错误: roomID=%s, type=%s, error=%v", roomID, msgType, err)
		h.roomManager.mu.RUnlock()
		return
	}

	roomInfo.mu.RLock()
	playerCount := len(roomInfo.Players)
	sentCount := 0
	for userID, player := range roomInfo.Players {
		player.mu.Lock()
		if player.IsOnline && player.Conn != nil {
			if err := player.Conn.WriteMessage(websocket.TextMessage, jsonMsg); err != nil {
				log.Printf("[WebSocket] 广播消息发送错误: roomID=%s, userID=%d, type=%s, error=%v", roomID, userID, msgType, err)
			} else {
				sentCount++
			}
		}
		player.mu.Unlock()
	}
	roomInfo.mu.RUnlock()
	h.roomManager.mu.RUnlock()

	log.Printf("[WebSocket] 广播消息完成: roomID=%s, type=%s, 发送成功=%d/%d", roomID, msgType, sentCount, playerCount)
}

// sendRoomStatus 发送房间状态
func (h *ArenaHandler) sendRoomStatus(player *PlayerConnection) {
	if player.RoomID == "" {
		return
	}

	// 获取房间信息
	roomInfo, err := h.arenaService.GetRoomInfo(player.RoomID)
	if err != nil {
		h.sendError(player, "Failed to get room info")
		return
	}

	// 发送房间状态
	h.sendMessage(player, "room_status", roomInfo)
}

// broadcastRoomStatus 广播房间状态
func (h *ArenaHandler) broadcastRoomStatus(roomID string) {
	// 获取房间信息
	roomInfo, err := h.arenaService.GetRoomInfo(roomID)
	if err != nil {
		log.Printf("Failed to get room info: %v", err)
		return
	}

	// 广播房间状态
	h.broadcastMessage(roomID, "room_status", roomInfo)
}

// broadcastCountdown 广播倒计时
func (h *ArenaHandler) broadcastCountdown(roomID string, seconds int) {
	for i := seconds; i > 0; i-- {
		h.broadcastMessage(roomID, "countdown", map[string]interface{}{
			"countdown": i,
		})
		time.Sleep(1 * time.Second)
	}
}

// startGame 开始游戏
func (h *ArenaHandler) startGame(roomID string) {
	log.Printf("[WebSocket] 开始游戏: roomID=%s", roomID)

	// 更新房间状态
	if err := h.arenaService.StartRoom(roomID); err != nil {
		log.Printf("[WebSocket] 更新房间状态失败: roomID=%s, error=%v", roomID, err)
		return
	}

	log.Printf("[WebSocket] 更新房间状态成功: roomID=%s, status=running", roomID)

	h.roomManager.mu.Lock()
	roomInfo, exists := h.roomManager.rooms[roomID]
	if !exists {
		log.Printf("[WebSocket] 房间不存在: roomID=%s", roomID)
		h.roomManager.mu.Unlock()
		return
	}

	// 生成题目
	log.Printf("[WebSocket] 开始生成题目: roomID=%s, level=%s", roomID, roomInfo.Level)
	questions, err := h.arenaService.GenerateRoomQuestions(roomID, roomInfo.Level, 10)
	if err != nil {
		log.Printf("[WebSocket] 生成题目失败: roomID=%s, level=%s, error=%v", roomID, roomInfo.Level, err)
		h.roomManager.mu.Unlock()
		return
	}

	log.Printf("[WebSocket] 生成题目成功: roomID=%s, 题目数量=%d", roomID, len(questions))

	// 更新房间信息
	roomInfo.Status = model.RoomStatusRunning
	roomInfo.Questions = make([]interface{}, len(questions))
	for i, q := range questions {
		roomInfo.Questions[i] = q
	}
	roomInfo.StartTime = time.Now()

	// 获取玩家数量
	playerCount := len(roomInfo.Players)
	h.roomManager.mu.Unlock()

	log.Printf("[WebSocket] 更新房间内存状态: roomID=%s, status=running, 玩家数量=%d", roomID, playerCount)

	// 广播游戏开始消息
	h.broadcastMessage(roomID, "game_start", map[string]interface{}{
		"questions": questions,
		"timeLimit": roomInfo.TimeLimit,
	})

	// 更新所有玩家状态
	log.Printf("[WebSocket] 更新所有玩家状态: roomID=%s", roomID)
	for _, player := range roomInfo.Players {
		player.mu.Lock()
		player.Status = model.PlayerStatusPlaying
		player.mu.Unlock()
		h.arenaService.UpdatePlayerStatus(player.UserID, model.PlayerStatusPlaying)
		log.Printf("[WebSocket] 更新玩家状态: roomID=%s, userID=%d, status=playing", roomID, player.UserID)
	}

	// 启动游戏计时器
	log.Printf("[WebSocket] 启动游戏计时器: roomID=%s, timeLimit=%d秒", roomID, roomInfo.TimeLimit)
	time.AfterFunc(time.Duration(roomInfo.TimeLimit)*time.Second, func() {
		log.Printf("[WebSocket] 游戏时间到，自动结束游戏: roomID=%s", roomID)
		h.finishGame(roomID)
	})
}

// finishGame 结束游戏
func (h *ArenaHandler) finishGame(roomID string) {
	log.Printf("[WebSocket] 结束游戏: roomID=%s", roomID)

	// 更新房间状态
	if err := h.arenaService.FinishRoom(roomID); err != nil {
		log.Printf("[WebSocket] 更新房间状态失败: roomID=%s, error=%v", roomID, err)
		return
	}

	log.Printf("[WebSocket] 更新房间状态成功: roomID=%s, status=finished", roomID)

	// 计算结果
	log.Printf("[WebSocket] 开始计算结果: roomID=%s", roomID)
	if err := h.arenaService.CalculateResults(roomID); err != nil {
		log.Printf("[WebSocket] 计算结果失败: roomID=%s, error=%v", roomID, err)
		return
	}

	log.Printf("[WebSocket] 计算结果成功: roomID=%s", roomID)

	h.roomManager.mu.Lock()
	roomInfo, exists := h.roomManager.rooms[roomID]
	if !exists {
		log.Printf("[WebSocket] 房间不存在: roomID=%s", roomID)
		h.roomManager.mu.Unlock()
		return
	}
	roomInfo.Status = model.RoomStatusFinished
	h.roomManager.mu.Unlock()

	log.Printf("[WebSocket] 更新房间内存状态: roomID=%s, status=finished", roomID)

	// 获取玩家列表
	log.Printf("[WebSocket] 获取玩家列表: roomID=%s", roomID)
	players, err := h.arenaService.GetRoomPlayers(roomID)
	if err != nil {
		log.Printf("[WebSocket] 获取玩家列表失败: roomID=%s, error=%v", roomID, err)
		return
	}

	log.Printf("[WebSocket] 获取玩家列表成功: roomID=%s, 玩家数量=%d", roomID, len(players))

	// 广播游戏结果
	h.broadcastMessage(roomID, "game_result", map[string]interface{}{
		"ranking":      players,
		"totalPlayers": len(players),
		"finishTime":   time.Now(),
	})

	log.Printf("[WebSocket] 广播游戏结果完成: roomID=%s", roomID)
}

// sendGameStatus 发送游戏状态
func (h *ArenaHandler) sendGameStatus(player *PlayerConnection) {
	if player.RoomID == "" {
		return
	}

	h.roomManager.mu.RLock()
	roomInfo, exists := h.roomManager.rooms[player.RoomID]
	if !exists {
		h.roomManager.mu.RUnlock()
		return
	}

	playerInfo, exists := roomInfo.Players[player.UserID]
	if !exists {
		h.roomManager.mu.RUnlock()
		return
	}

	// 计算剩余时间
	elapsedTime := time.Since(roomInfo.StartTime)
	timeRemaining := roomInfo.TimeLimit - int(elapsedTime.Seconds())
	if timeRemaining < 0 {
		timeRemaining = 0
	}

	// 获取题目
	questions := roomInfo.Questions
	currentIndex := playerInfo.CurrentIndex
	h.roomManager.mu.RUnlock()

	// 发送游戏状态
	h.sendMessage(player, "game_status", map[string]interface{}{
		"questions":     questions,
		"currentIndex":  currentIndex,
		"timeRemaining": timeRemaining,
	})

	// 广播玩家进度
	h.broadcastPlayerProgress(player.RoomID)
}

// broadcastPlayerProgress 广播玩家进度
func (h *ArenaHandler) broadcastPlayerProgress(roomID string) {
	h.roomManager.mu.RLock()
	roomInfo, exists := h.roomManager.rooms[roomID]
	if !exists {
		h.roomManager.mu.RUnlock()
		return
	}

	// 收集玩家进度
	progress := make(map[uint]map[string]interface{})
	for userID, player := range roomInfo.Players {
		player.mu.Lock()
		progress[userID] = map[string]interface{}{
			"currentIndex": player.CurrentIndex,
			"score":        player.Score,
			"correctCount": player.CorrectCount,
			"wrongCount":   player.WrongCount,
			"isOnline":     player.IsOnline,
			"lastActiveAt": player.LastActiveAt,
		}
		player.mu.Unlock()
	}
	h.roomManager.mu.RUnlock()

	// 广播玩家进度
	h.broadcastMessage(roomID, "player_progress", map[string]interface{}{
		"progress": progress,
	})
}

// sendGameResult 发送游戏结果
func (h *ArenaHandler) sendGameResult(player *PlayerConnection) {
	if player.RoomID == "" {
		return
	}

	// 获取玩家列表
	players, err := h.arenaService.GetRoomPlayers(player.RoomID)
	if err != nil {
		log.Printf("Failed to get room players: %v", err)
		return
	}

	// 查找当前玩家结果
	var myResult interface{}
	for _, p := range players {
		if p.UserID == player.UserID {
			myResult = p
			break
		}
	}

	// 发送游戏结果
	h.sendMessage(player, "game_result", map[string]interface{}{
		"ranking":      players,
		"myResult":     myResult,
		"totalPlayers": len(players),
	})
}

// RegisterRoutes 注册路由
func (h *ArenaHandler) RegisterRoutes(router *gin.RouterGroup, authMiddleware gin.HandlerFunc) {
	// 公开API
	router.GET("/levels", h.GetLevels)
	router.GET("/rankings/today", h.GetTodayRankings)

	// 需要认证的API
	authRouter := router.Group("/")
	authRouter.Use(authMiddleware)
	{
		authRouter.GET("/rankings/user", h.GetUserRankings)
		authRouter.GET("/ws", h.HandleWebSocket)
	}
}
