package handler

import (
	"log"
	"net/http"
	"sync"
	"time"

	"xuebalaile-api/internal/app/service"
	"xuebalaile-api/internal/pkg/auth"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// WebSocket消息类型
const (
	MsgTypeJoinRoom        = "join_room"
	MsgTypeLeaveRoom       = "leave_room"
	MsgTypeRoomStatus      = "room_status"
	MsgTypeCountdown       = "countdown"
	MsgTypeGameStart       = "game_start"
	MsgTypeQuestion        = "question"
	MsgTypeSubmitAnswer    = "submit_answer"
	MsgTypePlayerProgress  = "player_progress"
	MsgTypeGameResult      = "game_result"
	MsgTypeError           = "error"
	MsgTypePing            = "ping"
	MsgTypePong            = "pong"
	MsgTypeReconnect       = "reconnect"
	MsgTypeReconnectResult = "reconnect_result"
)

// WSMessage WebSocket消息
type WSMessage struct {
	Type    string      `json:"type"`
	Data    interface{} `json:"data"`
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
}

// PlayerConnection 玩家连接
type PlayerConnection struct {
	Conn      *websocket.Conn
	UserID    uint
	Nickname  string
	Avatar    string
	SessionID string
	RoomID    string
	LastPing  time.Time
	mu        sync.Mutex
}

// RoomManager 房间管理器
type RoomManager struct {
	rooms        map[string]*RoomInfo // roomID -> RoomInfo
	players      map[uint]*PlayerInfo // userID -> PlayerInfo
	sessions     map[string]uint      // sessionID -> userID
	mu           sync.RWMutex
	arenaService service.ArenaService
}

// RoomInfo 房间信息
type RoomInfo struct {
	RoomID     string
	Status     string
	Level      string
	Players    map[uint]*PlayerInfo // userID -> PlayerInfo
	Questions  []interface{}
	StartTimer *time.Timer
	StartTime  time.Time
	TimeLimit  int
	mu         sync.RWMutex
}

// PlayerInfo 玩家信息
type PlayerInfo struct {
	UserID       uint
	Nickname     string
	Avatar       string
	Conn         *websocket.Conn
	SessionID    string
	Status       string
	CurrentIndex int
	Score        int
	CorrectCount int
	WrongCount   int
	IsOnline     bool
	LastActiveAt time.Time
	mu           sync.Mutex
}

// ArenaHandler 打擂处理器
type ArenaHandler struct {
	arenaService service.ArenaService
	userService  service.UserService
	jwtService   *auth.JWTService
	roomManager  *RoomManager
	upgrader     websocket.Upgrader
}

// NewArenaHandler 创建打擂处理器
func NewArenaHandler(arenaService service.ArenaService, userService service.UserService, jwtService *auth.JWTService) *ArenaHandler {
	roomManager := &RoomManager{
		rooms:        make(map[string]*RoomInfo),
		players:      make(map[uint]*PlayerInfo),
		sessions:     make(map[string]uint),
		arenaService: arenaService,
	}

	return &ArenaHandler{
		arenaService: arenaService,
		userService:  userService,
		jwtService:   jwtService,
		roomManager:  roomManager,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		},
	}
}

// GetLevels 获取难度级别
func (h *ArenaHandler) GetLevels(c *gin.Context) {
	log.Printf("[HTTP] 收到请求: 获取难度级别, IP=%s, URL=%s", c.ClientIP(), c.Request.URL.String())

	// 获取难度级别列表
	levels, err := h.arenaService.GetCompetitionLevels()
	if err != nil {
		log.Printf("[HTTP] 获取难度级别失败: error=%v", err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	log.Printf("[HTTP] 获取难度级别成功: 返回%d个级别", len(levels))
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    levels,
	})
}

// GetTodayRankings 获取今日排行榜
func (h *ArenaHandler) GetTodayRankings(c *gin.Context) {
	subjectType := c.DefaultQuery("subjectType", "math")
	level := c.DefaultQuery("level", "")

	log.Printf("[HTTP] 收到请求: 获取今日排行榜, IP=%s, URL=%s, subjectType=%s, level=%s",
		c.ClientIP(), c.Request.URL.String(), subjectType, level)

	// 获取今日排行榜
	rankings, err := h.arenaService.GetTodayTopRankings(subjectType, level, 5)
	if err != nil {
		log.Printf("[HTTP] 获取今日排行榜失败: subjectType=%s, level=%s, error=%v", subjectType, level, err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	log.Printf("[HTTP] 获取今日排行榜成功: subjectType=%s, level=%s, 返回%d条记录", subjectType, level, len(rankings))
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    rankings,
	})
}

// GetUserRankings 获取用户排行榜
func (h *ArenaHandler) GetUserRankings(c *gin.Context) {
	log.Printf("[HTTP] 收到请求: 获取用户排行榜, IP=%s, URL=%s", c.ClientIP(), c.Request.URL.String())

	userID, exists := c.Get("userID")
	if !exists {
		log.Printf("[HTTP] 获取用户排行榜失败: 未授权访问")
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Unauthorized",
		})
		return
	}

	log.Printf("[HTTP] 获取用户排行榜: userID=%d", userID.(uint))

	// 获取用户排行榜
	rankings, err := h.arenaService.GetUserRankingHistory(userID.(uint), 10)
	if err != nil {
		log.Printf("[HTTP] 获取用户排行榜失败: userID=%d, error=%v", userID.(uint), err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": err.Error(),
		})
		return
	}

	log.Printf("[HTTP] 获取用户排行榜成功: userID=%d, 返回%d条记录", userID.(uint), len(rankings))
	c.JSON(http.StatusOK, gin.H{
		"code":    0,
		"message": "success",
		"data":    rankings,
	})
}

// HandleWebSocket 处理WebSocket连接
func (h *ArenaHandler) HandleWebSocket(c *gin.Context) {
	// 记录请求信息
	log.Printf("[WebSocket] 收到连接请求: IP=%s, URL=%s", c.ClientIP(), c.Request.URL.String())

	// 获取token
	token := c.Query("token")
	if token == "" {
		log.Printf("[WebSocket] 连接失败: 未提供token")
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Unauthorized",
		})
		return
	}

	// 验证token
	claims, err := h.jwtService.ValidateToken(token)
	if err != nil {
		log.Printf("[WebSocket] 连接失败: token无效, error=%v", err)
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Invalid token",
		})
		return
	}

	// 获取用户信息
	userID := claims.UserID
	log.Printf("[WebSocket] 验证token成功: userID=%d", userID)

	user, err := h.userService.GetUserByID(userID)
	if err != nil {
		log.Printf("[WebSocket] 连接失败: 获取用户信息失败, userID=%d, error=%v", userID, err)
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get user info",
		})
		return
	}

	log.Printf("[WebSocket] 获取用户信息成功: userID=%d, nickname=%s", userID, user.Nickname)

	// 升级HTTP连接为WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("[WebSocket] 升级连接失败: userID=%d, error=%v", userID, err)
		return
	}

	log.Printf("[WebSocket] 升级连接成功: userID=%d, nickname=%s", userID, user.Nickname)

	// 创建玩家连接
	playerConn := &PlayerConnection{
		Conn:     conn,
		UserID:   userID,
		Nickname: user.Nickname,
		Avatar:   user.Avatar,
		LastPing: time.Now(),
	}

	// 处理WebSocket消息
	go h.handleMessages(playerConn)
}
