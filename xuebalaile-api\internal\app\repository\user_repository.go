package repository

import (
	"errors"

	"xuebalaile-api/internal/app/model"

	"gorm.io/gorm"
)

// UserRepository 用户存储库接口
type UserRepository interface {
	Create(user *model.User) error
	FindByID(id uint) (*model.User, error)
	FindByOpenID(openID string, platform string) (*model.User, error)
	Update(user *model.User) error
}

// userRepository 用户存储库实现
type userRepository struct {
	db *gorm.DB
}

// NewUserRepository 创建用户存储库
func NewUserRepository(db *gorm.DB) UserRepository {
	return &userRepository{db: db}
}

// Create 创建用户
func (r *userRepository) Create(user *model.User) error {
	return r.db.Create(user).Error
}

// FindByID 根据ID查找用户
func (r *userRepository) FindByID(id uint) (*model.User, error) {
	var user model.User
	if err := r.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByOpenID 根据OpenID查找用户
func (r *userRepository) FindByOpenID(openID string, platform string) (*model.User, error) {
	var user model.User
	if err := r.db.Where("open_id = ? AND platform = ?", openID, platform).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// Update 更新用户
func (r *userRepository) Update(user *model.User) error {
	return r.db.Save(user).Error
}
