//设置环境(打包前修改此变量)  PROD/DEV
const ENV = "DEV2";
const UNI_APP = {};


if(ENV=="DEV1"){
	UNI_APP.BASE_URL = "http://127.0.0.1:8080/api";
    UNI_APP.WS_BASE_URL = "ws://127.0.0.1:8080/api";
}
if(ENV=="DEV2"){
    UNI_APP.BASE_URL = "http://192.168.0.115:8080/api";
    // 确保WebSocket URL是正确的，不要在末尾添加多余的路径
    UNI_APP.WS_BASE_URL = "ws://192.168.0.115:8080";
}
if(ENV=="DEV3"){
    UNI_APP.BASE_URL = "http://192.168.124.38:8080/api";
    UNI_APP.WS_BASE_URL = "ws://192.168.124.38:8080/api";
}
if(ENV=="DEV4"){
    UNI_APP.BASE_URL = "http://192.168.99.5:8080/api";
    UNI_APP.WS_BASE_URL = "ws://192.168.99.5:8080/api";
}

if(ENV=="PROD"){
    UNI_APP.BASE_URL = "http://91.149.239.233:8080/api";
    UNI_APP.WS_BASE_URL = "ws://91.149.239.233:8080/api";
}

export default UNI_APP