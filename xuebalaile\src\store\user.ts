/**
 * 用户状态管理
 */
import { ref, reactive } from 'vue';
import { login as apiLogin, getUserInfo } from '@/api/user';
import type { UserInfo } from '@/types/user';


// 用户信息
export const userInfo = reactive<UserInfo>({
  id: 0,
  nickname: '',
  avatar: '',
  openid: '',
  platform: '',
});

// 登录状态
export const isLoggedIn = ref(false);

// 初始化用户状态
export const initUserState = () => {
  isLoggedIn.value =isLoginValid()
  console.error('initUserState, isLoggedIn.value:', isLoggedIn.value);
  if (isLoggedIn.value) {
    const cachedUserInfo = uni.getStorageSync('userInfo');
    Object.assign(userInfo, cachedUserInfo);
  }
  return isLoggedIn.value;
};

// 登录方法
export const login = async (code: string, platform: 'wechat' | 'xiaohongshu') => {
  try {
    const res = await apiLogin({ code, platform });

    // 保存token和用户信息
    uni.setStorageSync('token', res.token);
    uni.setStorageSync('expiresAt', res.expiresAt);
    uni.setStorageSync('notBefore', res.notBefore);
    uni.setStorageSync('userInfo', res.userInfo);

    // 更新状态
    isLoggedIn.value = true;
    Object.assign(userInfo, res.userInfo);

    return true;
  } catch (error) {
    console.error('登录失败:', error);
    return false;
  }
};


/**
 * 判断登录信息是否有效
 * 检查条件：
 * 1. 存在token
 * 2. 当前时间在有效期内（大于notBefore且小于expiresAt）
 */
export const isLoginValid = (): boolean => {
  try {
    // 获取存储的登录信息
    const token = uni.getStorageSync('token');
    const expiresAt = uni.getStorageSync('expiresAt');
    const notBefore = uni.getStorageSync('notBefore');
    // console.error('isLoginValid, token:', token);
    // console.error('isLoginValid, expiresAt:', expiresAt);
    // console.error('isLoginValid, notBefore:', notBefore);
    
    // 检查必要字段是否存在
    if (!token || !expiresAt || !notBefore) {
      return false;
    }
    
    // 转换为数字类型（如果存储的是字符串）
    // 解析 RFC3339 时间字符串
    const expiresTime = new Date(expiresAt).getTime() / 1000; // 转为秒级时间戳
    const notBeforeTime = new Date(notBefore).getTime() / 1000;
    const currentTime = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
    
    // console.error('isLoginValid, currentTime:', currentTime);
    // console.error('isLoginValid, notBeforeTime:', notBeforeTime);
    // console.error('isLoginValid, expiresTime:', expiresTime);
    // 检查时间有效性
    return currentTime >= notBeforeTime && currentTime < expiresTime;
  } catch (error) {
    console.error('检查登录状态失败:', error);
    return false;
  }
};


// 获取最新用户信息
export const refreshUserInfo = async () => {
  try {
    const res = await getUserInfo();

    // 更新用户信息
    Object.assign(userInfo, res);
    uni.setStorageSync('userInfo', res);

    return true;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return false;
  }
};

// 退出登录
export const logout = () => {
  // 清除本地存储
  uni.removeStorageSync('token');
  uni.removeStorageSync('expiresAt');
  uni.removeStorageSync('notBefore');
  uni.removeStorageSync('userInfo');

  // 重置状态
  isLoggedIn.value = false;
  Object.assign(userInfo, {
    id: 0,
    nickname: '',
    avatar: '',
    openid: '',
    platform: '',
  });

  // 跳转到登录页
  uni.navigateTo({ url: '/pages/login/login' });
};
