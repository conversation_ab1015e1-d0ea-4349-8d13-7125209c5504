package service

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"xuebalaile-api/internal/app/model"
	"xuebalaile-api/internal/app/repository"
	"xuebalaile-api/internal/pkg/auth"
	"xuebalaile-api/internal/pkg/config"
)

// UserService 用户服务接口
type UserService interface {
	Login(code string, platform string) (*model.UserResponse, string, *time.Time, *time.Time, error)
	GetUserByID(id uint) (*model.UserResponse, error)
}

// userService 用户服务实现
type userService struct {
	userRepo       repository.UserRepository
	jwtService     *auth.JWTService
	wechatCfg      config.WeChatConfig
	xiaohongshuCfg config.XiaoHongShuConfig
}

// NewUserService 创建用户服务
func NewUserService(
	userRepo repository.UserRepository,
	jwtService *auth.JWTService,
	cfg *config.Config,
) UserService {
	return &userService{
		userRepo:       userRepo,
		jwtService:     jwtService,
		wechatCfg:      cfg.WeChat,
		xiaohongshuCfg: cfg.XiaoHongShu,
	}
}

// 微信登录凭证响应结构体
type wechatLoginResponse struct {
	OpenID     string `json:"openid"`
	SessionKey string `json:"sessionKey"`
	UnionID    string `json:"unionid,omitempty"`
	ErrCode    int    `json:"errcode,omitempty"`
	ErrMsg     string `json:"errmsg,omitempty"`
}

// Login 用户登录
func (s *userService) Login(code string, platform string) (*model.UserResponse, string, *time.Time, *time.Time, error) {
	var openID string
	var nickname string
	var avatar string
	var err error

	// 根据平台获取用户信息
	switch platform {
	case "wechat":
		openID, _, err = s.getWeChatSessionInfo(code)
	case "xiaohongshu":
		openID, nickname, avatar, err = s.getXiaoHongShuUserInfo(code)
	default:
		return nil, "", nil, nil, errors.New("unsupported platform")
	}

	if err != nil {
		return nil, "", nil, nil, err
	}

	// 查找用户
	user, err := s.userRepo.FindByOpenID(openID, platform)
	if err != nil {
		return nil, "", nil, nil, err
	}

	// 如果用户不存在，创建新用户
	if user == nil {
		user = &model.User{
			Nickname: nickname,
			Avatar:   avatar,
			OpenID:   openID,
			Platform: platform,
		}
		if err := s.userRepo.Create(user); err != nil {
			return nil, "", nil, nil, err
		}
	} else {
		// 更新用户信息
		user.Nickname = nickname
		user.Avatar = avatar
		if err := s.userRepo.Update(user); err != nil {
			return nil, "", nil, nil, err
		}
	}

	// 生成JWT令牌
	token, expiresAt, notBefore, err := s.jwtService.GenerateToken(user.ID, platform)
	if err != nil {
		return nil, "", nil, nil, err
	}

	return user.ToResponse(), token, expiresAt, notBefore, nil
}

// GetUserByID 根据ID获取用户
func (s *userService) GetUserByID(id uint) (*model.UserResponse, error) {
	user, err := s.userRepo.FindByID(id)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, errors.New("user not found")
	}
	return user.ToResponse(), nil
}

// getWeChatSessionInfo 通过微信API获取openid和session_key
// 参数 code: 微信前端提供的登录凭证
// 返回: openid, session_key, error
func (s *userService) getWeChatSessionInfo(code string) (string, string, error) {
	// 配置信息（生产环境建议从配置读取）
	appID := s.wechatCfg.AppID
	appSecret := s.wechatCfg.Secret

	// 构造微信API请求URL
	url := fmt.Sprintf(
		"https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
		appID, appSecret, code,
	)

	// 发送HTTP GET请求
	resp, err := http.Get(url)
	if err != nil {
		return "", "", fmt.Errorf("微信API请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 解析JSON响应
	var result wechatLoginResponse
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return "", "", fmt.Errorf("响应解析失败: %v", err)
	}

	// 检查微信错误码
	if result.ErrCode != 0 {
		return "", "", fmt.Errorf("微信API错误[%d]: %s", result.ErrCode, result.ErrMsg)
	}

	if len(result.OpenID) == 0 {
		return "", "", fmt.Errorf("result.OpenID is empty")
	}

	return result.OpenID, result.SessionKey, nil
}

// getXiaoHongShuUserInfo 获取小红书用户信息
func (s *userService) getXiaoHongShuUserInfo(code string) (string, string, string, error) {
	// 在实际项目中，这里应该调用小红书API获取用户信息
	// 这里为了简化，直接返回模拟数据
	openID := fmt.Sprintf("xhs_openid_%s", code)
	nickname := "小红书用户"
	avatar := "https://example.com/avatar.png"
	return openID, nickname, avatar, nil
}
