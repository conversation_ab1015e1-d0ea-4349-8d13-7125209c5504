<template>
  <view class="container">
    <view class="result-card">
      <view class="result-header">
        <text class="result-title">答题结束</text>
        <text class="result-subtitle">本次答题情况</text>
      </view>

      <view class="result-stats">
        <view class="stat-item">
          <text class="stat-value">{{ quizResult.totalQuestions }}</text>
          <text class="stat-label">总题数</text>
        </view>
        <view class="stat-item correct">
          <text class="stat-value">{{ quizResult.correctCount }}</text>
          <text class="stat-label">答对</text>
        </view>
        <view class="stat-item wrong">
          <text class="stat-value">{{ quizResult.wrongCount }}</text>
          <text class="stat-label">答错</text>
        </view>
<!--        <view class="stat-item">
          <text class="stat-value">{{ formatTime(quizResult.timeUsed) }}</text>
          <text class="stat-label">用时</text>
        </view> -->
        <view class="stat-item">
          <text class="stat-value">{{ quizResult.correctCount }}</text>
          <text class="stat-label">得分</text>
        </view>
      </view>

      <view class="divider"></view>

      <view class="wrong-questions" v-if="quizResult.wrongCount > 0">
        <text class="section-title">错题回顾</text>

        <view class="question-list">
          <view v-for="(item, index) in quizResult.wrongQuestions" :key="index" class="question-item">
            <view class="question-info">
              <text class="question-text">{{ item.question.question }}</text>
              <view class="answer-info">
                <text class="user-answer">你的答案: {{ item.userAnswer }}</text>
                <text class="correct-answer">正确答案: {{ item.question.answer }}</text>
              </view>
            </view>
          </view>
        </view>

        <button class="practice-btn" @click="goToWrongQuestions">练习错题</button>
      </view>

      <view class="perfect-score" v-else>
        <image class="perfect-icon" src="/static/images/perfect.png" mode="aspectFit" />
        <text class="perfect-text">太棒了！全部答对</text>
      </view>
    </view>

    <view class="action-buttons">
      <button class="action-btn retry" @click="goToSelect">再来一次</button>
      <button class="action-btn home" @click="goToHome">返回首页</button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { onLoad } from '@dcloudio/uni-app';
import { quizResult, resetQuiz } from '../../store/math';

// 格式化时间
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 页面加载时检查结果
onLoad(() => {
  if (quizResult.totalQuestions === 0) {
    uni.showToast({
      title: '请先完成答题',
      icon: 'none'
    });

    // 返回选择页面
    setTimeout(() => {
      goToSelect();
    }, 1500);
  }
});

// 跳转到错题练习
const goToWrongQuestions = () => {
  uni.navigateTo({ url: '/pages/math/wrong' });
};

// 跳转到选择页面
const goToSelect = () => {
  // 重置状态
  resetQuiz();

  // 返回选择页面
  // uni.redirectTo({ url: '/pages/math/select' });
   // 使用 switchTab 跳转到 tabBar 页面
    uni.switchTab({ url: '/pages/math/select' });
};

// 返回首页
const goToHome = () => {
  // 重置状态
  resetQuiz();

  // 返回首页
  uni.switchTab({ url: '/pages/index/index' });
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 30rpx;
}

.result-card {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
}

.result-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.result-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
  display: block;
}

.result-subtitle {
  font-size: 28rpx;
  color: #999999;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999999;
}

.correct .stat-value {
  color: #4cd964;
}

.wrong .stat-value {
  color: #ff3b30;
}

.divider {
  height: 2rpx;
  background-color: #eeeeee;
  margin: 20rpx 0 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 30rpx;
  display: block;
}

.question-list {
  margin-bottom: 40rpx;
}

.question-item {
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
}

.question-info {
  display: flex;
  flex-direction: column;
}

.question-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 20rpx;
}

.answer-info {
  display: flex;
  justify-content: space-between;
}

.user-answer {
  font-size: 28rpx;
  color: #ff3b30;
}

.correct-answer {
  font-size: 28rpx;
  color: #4cd964;
}

.practice-btn {
  height: 90rpx;
  background-color: #3cc51f;
  color: #ffffff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.perfect-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.perfect-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.perfect-text {
  font-size: 36rpx;
  font-weight: bold;
  color: #4cd964;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 90rpx;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
}

.retry {
  background-color: #007aff;
  color: #ffffff;
}

.home {
  background-color: #f0f0f0;
  color: #333333;
}
</style>
